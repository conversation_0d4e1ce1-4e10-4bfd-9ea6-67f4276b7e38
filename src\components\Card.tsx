import React from 'react';
import { View, ViewStyle, StyleProp } from 'react-native';
import { Colors, BorderRadius, Shadows, Spacing } from '../constants/theme';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'student' | 'teacher';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  style?: StyleProp<ViewStyle>;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  style,
}) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: BorderRadius.lg,
      backgroundColor: Colors.background.primary,
    };

    // Padding styles
    switch (padding) {
      case 'none':
        break;
      case 'sm':
        baseStyle.padding = Spacing.sm;
        break;
      case 'lg':
        baseStyle.padding = Spacing.lg;
        break;
      default:
        baseStyle.padding = Spacing.md;
    }

    // Variant styles
    switch (variant) {
      case 'elevated':
        Object.assign(baseStyle, Shadows.md);
        break;
      case 'outlined':
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = Colors.neutral[200];
        break;
      case 'student':
        Object.assign(baseStyle, Shadows.sm);
        baseStyle.borderLeftWidth = 4;
        baseStyle.borderLeftColor = Colors.student.primary;
        break;
      case 'teacher':
        Object.assign(baseStyle, Shadows.sm);
        baseStyle.borderLeftWidth = 4;
        baseStyle.borderLeftColor = Colors.teacher.primary;
        break;
      default:
        // Default shadow - using sm instead
        Object.assign(baseStyle, Shadows.sm);
    }

    return baseStyle;
  };

  return <View style={[getCardStyle(), style]}>{children}</View>;
};

export default Card;
