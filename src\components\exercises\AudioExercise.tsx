import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

interface AudioExerciseProps {
  word: string;
  phonetic: string;
  sampleAudioUrl: string;
  onRecord: (recordedUrl: string) => void;
  onPlaySample: () => void;
  recordedAudioUrl?: string;
  onPlayRecorded?: () => void;
  instruction?: string;
}

const AudioExercise: React.FC<AudioExerciseProps> = ({
  word,
  phonetic,
  sampleAudioUrl,
  onRecord,
  onPlaySample,
  recordedAudioUrl,
  onPlayRecorded,
  instruction = "Press record and pronounce the word",
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.instructionContainer}>
        <Text style={styles.instruction}>{instruction}</Text>
      </View>

      <View style={styles.wordContainer}>
        <Text style={styles.word}>{word}</Text>
        <Text style={styles.phonetic}>{phonetic}</Text>
      </View>

      <View style={styles.audioContainer}>
        <Text style={styles.label}>Sample Pronunciation:</Text>
        <Image
          style={styles.icon}
          source={{ uri: sampleAudioUrl }}
        />
        <Text style={styles.placeholder} onPress={onPlaySample}>▶ Play Sample</Text>
      </View>

      {recordedAudioUrl && (
        <View style={styles.audioContainer}>
          <Text style={styles.label}>Your Recording:</Text>
          <Text style={styles.placeholder} onPress={onPlayRecorded}>▶ Play Recorded</Text>
        </View>
      )}

      <View style={styles.recordContainer}>
        <Text style={styles.recordButton} onPress={() => onRecord('recordedAudioUrl')}>🔴 Record</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Spacing.md,
    backgroundColor: Colors.background.secondary,
    flex: 1,
  },
  instructionContainer: {
    backgroundColor: Colors.background.primary,
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.lg,
    elevation: 2,
  },
  instruction: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    textAlign: 'center',
  },
  wordContainer: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  word: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  phonetic: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  audioContainer: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  icon: {
    width: 50,
    height: 50,
  },
  placeholder: {
    fontSize: Typography.fontSize.sm,
    color: Colors.student.primary,
    marginTop: Spacing.sm,
  },
  recordContainer: {
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  recordButton: {
    fontSize: Typography.fontSize['2xl'],
    color: Colors.error[500],
    fontWeight: '700' as const,
  },
});

export default AudioExercise;

