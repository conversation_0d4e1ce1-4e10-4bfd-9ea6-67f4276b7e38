import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { supabase, Tables } from '../../services/supabase';

interface AchievementsState {
  achievements: Tables<'achievements'>[];
  loading: boolean;
  error: string | null;
}

const initialState: AchievementsState = {
  achievements: [],
  loading: false,
  error: null,
};

export const fetchAchievements = createAsyncThunk(
  'achievements/fetchAchievements',
  async (studentId: string, { rejectWithValue }) => {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .select('*')
        .eq('student_id', studentId)
        .order('earned_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const achievementsSlice = createSlice({
  name: 'achievements',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchAchievements.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAchievements.fulfilled, (state, action) => {
        state.loading = false;
        state.achievements = action.payload;
      })
      .addCase(fetchAchievements.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default achievementsSlice.reducer;
