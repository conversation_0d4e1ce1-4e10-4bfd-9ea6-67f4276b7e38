import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import slices
import authSlice from './slices/authSlice';
import userSlice from './slices/userSlice';
import lessonsSlice from './slices/lessonsSlice';
import exercisesSlice from './slices/exercisesSlice';
import progressSlice from './slices/progressSlice';
import achievementsSlice from './slices/achievementsSlice';
import gamificationSlice from './slices/gamificationSlice';
import offlineSlice from './slices/offlineSlice';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth', 'user', 'gamification'], // Only persist these reducers
  blacklist: ['lessons', 'exercises'], // Don't persist these (fetch fresh)
};

const rootReducer = combineReducers({
  auth: authSlice,
  user: userSlice,
  lessons: lessonsSlice,
  exercises: exercisesSlice,
  progress: progressSlice,
  achievements: achievementsSlice,
  gamification: gamificationSlice,
  offline: offlineSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
        ],
      },
    }),
  devTools: __DEV__,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
