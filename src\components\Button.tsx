import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  StyleProp,
} from 'react-native';
import { Colors, Typography, BorderRadius, Shadows, Layout } from '../constants/theme';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'student' | 'teacher';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
}) => {
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: BorderRadius.md,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      ...Shadows.sm,
    };

    // Size styles
    switch (size) {
      case 'sm':
        baseStyle.height = Layout.buttonHeight.sm;
        baseStyle.paddingHorizontal = 12;
        break;
      case 'lg':
        baseStyle.height = Layout.buttonHeight.lg;
        baseStyle.paddingHorizontal = 24;
        break;
      default:
        baseStyle.height = Layout.buttonHeight.md;
        baseStyle.paddingHorizontal = 16;
    }

    // Variant styles
    switch (variant) {
      case 'primary':
        baseStyle.backgroundColor = disabled ? Colors.neutral[300] : Colors.primary[500];
        break;
      case 'secondary':
        baseStyle.backgroundColor = disabled ? Colors.neutral[300] : Colors.secondary[500];
        break;
      case 'outline':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.borderWidth = 2;
        baseStyle.borderColor = disabled ? Colors.neutral[300] : Colors.primary[500];
        break;
      case 'ghost':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.shadowOpacity = 0;
        baseStyle.elevation = 0;
        break;
      case 'student':
        baseStyle.backgroundColor = disabled ? Colors.neutral[300] : Colors.student.primary;
        baseStyle.borderRadius = BorderRadius.lg;
        break;
      case 'teacher':
        baseStyle.backgroundColor = disabled ? Colors.neutral[300] : Colors.teacher.primary;
        break;
    }

    if (fullWidth) {
      baseStyle.width = '100%';
    }

    if (disabled) {
      baseStyle.opacity = 0.6;
    }

    return baseStyle;
  };

  const getTextStyle = (): TextStyle => {
    const baseTextStyle: TextStyle = {
      fontWeight: '600' as const,
      textAlign: 'center',
    };

    // Size styles
    switch (size) {
      case 'sm':
        baseTextStyle.fontSize = Typography.fontSize.sm;
        break;
      case 'lg':
        baseTextStyle.fontSize = Typography.fontSize.lg;
        break;
      default:
        baseTextStyle.fontSize = Typography.fontSize.base;
    }

    // Variant styles
    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'student':
      case 'teacher':
        baseTextStyle.color = Colors.text.inverse;
        break;
      case 'outline':
        baseTextStyle.color = disabled ? Colors.neutral[400] : Colors.primary[500];
        break;
      case 'ghost':
        baseTextStyle.color = disabled ? Colors.neutral[400] : Colors.primary[500];
        break;
    }

    return baseTextStyle;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? Colors.primary[500] : Colors.text.inverse}
        />
      ) : (
        <Text style={[getTextStyle(), textStyle]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
};

export default Button;
