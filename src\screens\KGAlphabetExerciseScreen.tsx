import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';

const alphabetData = [
  { id: '1', letter: 'A', sound: 'ay' },
  { id: '2', letter: 'B', sound: 'bee' },
  { id: '3', letter: 'C', sound: 'see' },
  { id: '4', letter: 'D', sound: 'dee' },
  { id: '5', letter: 'E', sound: 'ee' },
];

const KGAlphabetExerciseScreen = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const currentItem = alphabetData[currentIndex];

  const handlePronounce = () => {
    // In a real app, this would play an audio file for the sound.
    Alert.alert('Pronunciation', `The sound of ${currentItem.letter} is "${currentItem.sound}".`);
  };

  const handleNext = () => {
    if (currentIndex < alphabetData.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      Alert.alert('Exercise Complete', 'You have finished the alphabet exercise!');
      // Here, you would typically navigate back or show a completion summary.
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Learn the Alphabet</Text>
      <View style={styles.card}>
        <Text style={styles.letter}>{currentItem.letter}</Text>
        <TouchableOpacity style={styles.button} onPress={handlePronounce}>
          <Text style={styles.buttonText}>Hear Sound</Text>
        </TouchableOpacity>
      </View>
      <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
        <Text style={styles.nextButtonText}>Next Letter</Text>
      </TouchableOpacity>
      <Text style={styles.progressText}>
        Progress: {currentIndex + 1} / {alphabetData.length}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5FCFF',
  },
  title: {
    fontSize: 30,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#333',
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 8,
    marginBottom: 40,
    width: '80%',
    aspectRatio: 1, // Make it a square
  },
  letter: {
    fontSize: 120,
    fontWeight: 'bold',
    color: '#4CAF50', // Green color for letters
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#2196F3', // Blue color for button
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 30,
    marginTop: 15,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  nextButton: {
    backgroundColor: '#FF9800', // Orange color for next button
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    marginTop: 20,
    marginBottom: 20,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  progressText: {
    fontSize: 16,
    color: '#555',
  },
});

export default KGAlphabetExerciseScreen;
