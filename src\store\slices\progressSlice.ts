import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { supabase } from '../../services/supabase';

export interface ProgressData {
  id: string;
  student_id: string;
  lesson_id?: string;
  exercise_id?: string;
  status: 'not_started' | 'in_progress' | 'completed';
  score?: number;
  time_spent: number;
  attempts: number;
  last_attempt_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface SubjectProgress {
  subject: string;
  completed: number;
  total: number;
  averageScore: number;
  timeSpent: number;
  lastActivity: Date;
  streak: number;
}

export interface LearningAnalytics {
  totalTimeSpent: number;
  totalExercisesCompleted: number;
  averageScore: number;
  subjectsProgress: SubjectProgress[];
  weeklyProgress: {
    week: string;
    exercisesCompleted: number;
    timeSpent: number;
    averageScore: number;
  }[];
  strugglingAreas: string[];
  strengths: string[];
  recommendations: string[];
}

interface ProgressState {
  studentProgress: ProgressData[];
  analytics: LearningAnalytics | null;
  currentSession: {
    startTime?: Date;
    exerciseId?: string;
    timeSpent: number;
  };
  loading: boolean;
  error: string | null;
}

const initialState: ProgressState = {
  studentProgress: [],
  analytics: null,
  currentSession: {
    timeSpent: 0,
  },
  loading: false,
  error: null,
};

// Async thunks
export const fetchStudentProgress = createAsyncThunk(
  'progress/fetchStudentProgress',
  async (studentId: string, { rejectWithValue }) => {
    try {
      const { data, error } = await supabase
        .from('student_progress')
        .select(`
          *,
          lessons (
            title,
            subject,
            difficulty,
            grade_level
          ),
          exercises (
            title,
            subject,
            difficulty,
            grade_level,
            type
          )
        `)
        .eq('student_id', studentId)
        .order('updated_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateProgress = createAsyncThunk(
  'progress/updateProgress',
  async ({
    progressId,
    updates,
  }: {
    progressId: string;
    updates: Partial<ProgressData>;
  }, { rejectWithValue }) => {
    try {
      const { data, error } = await supabase
        .from('student_progress')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', progressId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const createProgress = createAsyncThunk(
  'progress/createProgress',
  async ({
    studentId,
    lessonId,
    exerciseId,
    status = 'not_started',
  }: {
    studentId: string;
    lessonId?: string;
    exerciseId?: string;
    status?: 'not_started' | 'in_progress' | 'completed';
  }, { rejectWithValue }) => {
    try {
      const { data, error } = await supabase
        .from('student_progress')
        .insert({
          student_id: studentId,
          lesson_id: lessonId,
          exercise_id: exerciseId,
          status,
          time_spent: 0,
          attempts: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const generateAnalytics = createAsyncThunk(
  'progress/generateAnalytics',
  async (studentId: string, { rejectWithValue }) => {
    try {
      // Fetch comprehensive progress data
      const { data: progressData, error: progressError } = await supabase
        .from('student_progress')
        .select(`
          *,
          lessons (
            title,
            subject,
            difficulty,
            grade_level
          ),
          exercises (
            title,
            subject,
            difficulty,
            grade_level,
            type,
            points
          )
        `)
        .eq('student_id', studentId);

      if (progressError) throw progressError;

      if (!progressData) return null;

      // Process analytics
      const totalTimeSpent = progressData.reduce((sum, p) => sum + p.time_spent, 0);
      const completedExercises = progressData.filter(p => p.status === 'completed');
      const totalExercisesCompleted = completedExercises.length;
      const averageScore = completedExercises.length > 0 
        ? completedExercises.reduce((sum, p) => sum + (p.score || 0), 0) / completedExercises.length 
        : 0;

      // Group by subjects
      const subjectMap = new Map<string, {
        completed: number;
        total: number;
        scores: number[];
        timeSpent: number;
        lastActivity?: Date;
      }>();

      progressData.forEach(progress => {
        const subject = progress.lessons?.subject || progress.exercises?.subject || 'Unknown';
        
        if (!subjectMap.has(subject)) {
          subjectMap.set(subject, {
            completed: 0,
            total: 0,
            scores: [],
            timeSpent: 0,
          });
        }

        const subjectData = subjectMap.get(subject)!;
        subjectData.total++;
        subjectData.timeSpent += progress.time_spent;

        if (progress.status === 'completed') {
          subjectData.completed++;
          if (progress.score) {
            subjectData.scores.push(progress.score);
          }
          if (progress.completed_at) {
            const completedDate = new Date(progress.completed_at);
            if (!subjectData.lastActivity || completedDate > subjectData.lastActivity) {
              subjectData.lastActivity = completedDate;
            }
          }
        }
      });

      const subjectsProgress: SubjectProgress[] = Array.from(subjectMap.entries()).map(([subject, data]) => ({
        subject,
        completed: data.completed,
        total: data.total,
        averageScore: data.scores.length > 0 ? data.scores.reduce((a, b) => a + b, 0) / data.scores.length : 0,
        timeSpent: data.timeSpent,
        lastActivity: data.lastActivity || new Date(),
        streak: 0, // Calculate streak separately if needed
      }));

      // Identify struggling areas (subjects with < 60% average or < 50% completion)
      const strugglingAreas = subjectsProgress
        .filter(s => s.averageScore < 60 || (s.completed / s.total) < 0.5)
        .map(s => s.subject);

      // Identify strengths (subjects with > 80% average and > 70% completion)
      const strengths = subjectsProgress
        .filter(s => s.averageScore > 80 && (s.completed / s.total) > 0.7)
        .map(s => s.subject);

      // Generate recommendations
      const recommendations: string[] = [];
      if (strugglingAreas.length > 0) {
        recommendations.push(`Focus more practice on: ${strugglingAreas.join(', ')}`);
      }
      if (averageScore < 70) {
        recommendations.push('Consider reviewing previous lessons before moving to new topics');
      }
      if (totalTimeSpent < 300) { // Less than 5 hours total
        recommendations.push('Try to spend at least 15-20 minutes daily on learning activities');
      }

      const analytics: LearningAnalytics = {
        totalTimeSpent,
        totalExercisesCompleted,
        averageScore,
        subjectsProgress,
        weeklyProgress: [], // Calculate weekly progress if needed
        strugglingAreas,
        strengths,
        recommendations,
      };

      return analytics;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const progressSlice = createSlice({
  name: 'progress',
  initialState,
  reducers: {
    startSession: (state, action: PayloadAction<{ exerciseId: string }>) => {
      state.currentSession = {
        startTime: new Date(),
        exerciseId: action.payload.exerciseId,
        timeSpent: 0,
      };
    },
    
    updateSessionTime: (state, action: PayloadAction<number>) => {
      state.currentSession.timeSpent = action.payload;
    },
    
    endSession: (state) => {
      state.currentSession = {
        timeSpent: 0,
      };
    },
    
    clearError: (state) => {
      state.error = null;
    },
  },
  
  extraReducers: (builder) => {
    builder
      // Fetch Progress
      .addCase(fetchStudentProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudentProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.studentProgress = action.payload;
      })
      .addCase(fetchStudentProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update Progress
      .addCase(updateProgress.fulfilled, (state, action) => {
        const index = state.studentProgress.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.studentProgress[index] = action.payload;
        }
      })
      
      // Create Progress
      .addCase(createProgress.fulfilled, (state, action) => {
        state.studentProgress.unshift(action.payload);
      })
      
      // Generate Analytics
      .addCase(generateAnalytics.pending, (state) => {
        state.loading = true;
      })
      .addCase(generateAnalytics.fulfilled, (state, action) => {
        state.loading = false;
        state.analytics = action.payload;
      })
      .addCase(generateAnalytics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { 
  startSession, 
  updateSessionTime, 
  endSession, 
  clearError 
} = progressSlice.actions;

export default progressSlice.reducer;
