import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/theme';
import { Card } from './';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  earned: boolean;
  earnedDate?: string;
  progress?: number;
  maxProgress?: number;
}

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  earned: boolean;
}

interface GamificationData {
  totalPoints: number;
  level: number;
  pointsToNextLevel: number;
  currentStreak: number;
  longestStreak: number;
  weeklyGoal: {
    target: number;
    current: number;
  };
  achievements: Achievement[];
  badges: Badge[];
  recentRewards: string[];
}

interface GamificationDashboardProps {
  data: GamificationData;
  onAchievementPress?: (achievement: Achievement) => void;
  onBadgePress?: (badge: Badge) => void;
  studentName: string;
}

const { width } = Dimensions.get('window');

const GamificationDashboard: React.FC<GamificationDashboardProps> = ({
  data,
  onAchievementPress,
  onBadgePress,
  studentName,
}) => {
  const levelProgress = ((data.pointsToNextLevel > 0 ? 
    (data.totalPoints / (data.totalPoints + data.pointsToNextLevel)) : 1) * 100);
  
  const weeklyProgress = (data.weeklyGoal.current / data.weeklyGoal.target) * 100;

  const getRarityColor = (rarity: Badge['rarity']) => {
    switch (rarity) {
      case 'common': return Colors.neutral[400];
      case 'rare': return Colors.student.accent;
      case 'epic': return Colors.student.purple;
      case 'legendary': return Colors.student.warning;
      default: return Colors.neutral[400];
    }
  };

  const renderLevelProgress = () => (
    <Card style={styles.levelCard}>
      <View style={styles.levelHeader}>
        <View style={styles.levelInfo}>
          <Text style={styles.levelTitle}>Level {data.level}</Text>
          <Text style={styles.levelSubtitle}>{studentName}</Text>
        </View>
        <View style={styles.pointsContainer}>
          <Text style={styles.pointsValue}>{data.totalPoints.toLocaleString()}</Text>
          <Text style={styles.pointsLabel}>Total Points</Text>
        </View>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${levelProgress}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {data.pointsToNextLevel > 0 
            ? `${data.pointsToNextLevel} points to Level ${data.level + 1}`
            : 'Max Level Reached!'
          }
        </Text>
      </View>
    </Card>
  );

  const renderStreakCard = () => (
    <Card style={styles.streakCard}>
      <View style={styles.streakHeader}>
        <Text style={styles.streakIcon}>🔥</Text>
        <View>
          <Text style={styles.streakTitle}>Learning Streak</Text>
          <Text style={styles.streakDays}>{data.currentStreak} days</Text>
        </View>
      </View>
      <Text style={styles.streakBest}>
        Best streak: {data.longestStreak} days
      </Text>
      {data.currentStreak >= 7 && (
        <View style={styles.streakBonus}>
          <Text style={styles.streakBonusText}>⭐ Weekly streak bonus!</Text>
        </View>
      )}
    </Card>
  );

  const renderWeeklyGoal = () => (
    <Card style={styles.goalCard}>
      <Text style={styles.goalTitle}>Weekly Goal</Text>
      <View style={styles.goalProgress}>
        <View style={styles.goalBar}>
          <View 
            style={[
              styles.goalFill, 
              { width: `${Math.min(weeklyProgress, 100)}%` }
            ]} 
          />
        </View>
        <Text style={styles.goalText}>
          {data.weeklyGoal.current} / {data.weeklyGoal.target} points
        </Text>
      </View>
      {weeklyProgress >= 100 && (
        <View style={styles.goalCompleted}>
          <Text style={styles.goalCompletedText}>🎉 Goal completed!</Text>
        </View>
      )}
    </Card>
  );

  const renderAchievements = () => (
    <View style={styles.achievementsSection}>
      <Text style={styles.sectionTitle}>Recent Achievements</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {data.achievements.slice(0, 5).map((achievement) => (
          <TouchableOpacity
            key={achievement.id}
            style={[
              styles.achievementCard,
              !achievement.earned && styles.achievementLocked
            ]}
            onPress={() => onAchievementPress?.(achievement)}
          >
            <Text style={styles.achievementIcon}>
              {achievement.earned ? achievement.icon : '🔒'}
            </Text>
            <Text style={styles.achievementTitle}>
              {achievement.title}
            </Text>
            <Text style={styles.achievementPoints}>
              {achievement.points} pts
            </Text>
            {achievement.progress !== undefined && achievement.maxProgress && (
              <View style={styles.achievementProgress}>
                <View style={styles.achievementProgressBar}>
                  <View 
                    style={[
                      styles.achievementProgressFill,
                      { width: `${(achievement.progress / achievement.maxProgress) * 100}%` }
                    ]}
                  />
                </View>
                <Text style={styles.achievementProgressText}>
                  {achievement.progress}/{achievement.maxProgress}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderBadges = () => (
    <View style={styles.badgesSection}>
      <Text style={styles.sectionTitle}>Earned Badges</Text>
      <View style={styles.badgesGrid}>
        {data.badges
          .filter(badge => badge.earned)
          .slice(0, 6)
          .map((badge) => (
            <TouchableOpacity
              key={badge.id}
              style={[
                styles.badgeCard,
                { borderColor: getRarityColor(badge.rarity) }
              ]}
              onPress={() => onBadgePress?.(badge)}
            >
              <Text style={styles.badgeIcon}>{badge.icon}</Text>
              <Text style={styles.badgeName}>{badge.name}</Text>
              <View style={[
                styles.rarityIndicator,
                { backgroundColor: getRarityColor(badge.rarity) }
              ]}>
                <Text style={styles.rarityText}>
                  {badge.rarity.toUpperCase()}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
      </View>
    </View>
  );

  const renderRecentRewards = () => (
    <View style={styles.rewardsSection}>
      <Text style={styles.sectionTitle}>Recent Rewards</Text>
      {data.recentRewards.length > 0 ? (
        <View style={styles.rewardsList}>
          {data.recentRewards.slice(0, 3).map((reward, index) => (
            <View key={index} style={styles.rewardItem}>
              <Text style={styles.rewardIcon}>🎁</Text>
              <Text style={styles.rewardText}>{reward}</Text>
            </View>
          ))}
        </View>
      ) : (
        <Text style={styles.noRewardsText}>
          Complete more exercises to earn rewards!
        </Text>
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>🎮 Your Learning Journey</Text>
        <Text style={styles.subtitle}>Keep up the amazing work, {studentName}!</Text>
      </View>

      {renderLevelProgress()}

      <View style={styles.statsRow}>
        {renderStreakCard()}
        {renderWeeklyGoal()}
      </View>

      {renderAchievements()}
      {renderBadges()}
      {renderRecentRewards()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  header: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  levelCard: {
    margin: Spacing.md,
    padding: Spacing.lg,
    backgroundColor: Colors.student.primary + '15',
    borderLeftWidth: 4,
    borderLeftColor: Colors.student.primary,
  },
  levelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  levelInfo: {
    flex: 1,
  },
  levelTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    color: Colors.student.primary,
    marginBottom: Spacing.xs / 2,
  },
  levelSubtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  pointsContainer: {
    alignItems: 'center',
  },
  pointsValue: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    color: Colors.student.primary,
  },
  pointsLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  progressContainer: {
    marginTop: Spacing.md,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.neutral[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.student.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginTop: Spacing.xs,
  },
  statsRow: {
    flexDirection: 'row',
    gap: Spacing.sm,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  streakCard: {
    flex: 1,
    padding: Spacing.md,
    backgroundColor: Colors.student.warning + '15',
    borderLeftWidth: 4,
    borderLeftColor: Colors.student.warning,
  },
  streakHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  streakIcon: {
    fontSize: Typography.fontSize['2xl'],
    marginRight: Spacing.sm,
  },
  streakTitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: '600' as const,
  },
  streakDays: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '700' as const,
    color: Colors.student.warning,
  },
  streakBest: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.tertiary,
    marginTop: Spacing.xs,
  },
  streakBonus: {
    backgroundColor: Colors.student.warning + '20',
    padding: Spacing.xs,
    borderRadius: BorderRadius.sm,
    marginTop: Spacing.sm,
  },
  streakBonusText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.student.warning,
    textAlign: 'center',
    fontWeight: '600' as const,
  },
  goalCard: {
    flex: 1,
    padding: Spacing.md,
    backgroundColor: Colors.student.secondary + '15',
    borderLeftWidth: 4,
    borderLeftColor: Colors.student.secondary,
  },
  goalTitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: '600' as const,
    marginBottom: Spacing.sm,
  },
  goalProgress: {
    marginBottom: Spacing.sm,
  },
  goalBar: {
    height: 6,
    backgroundColor: Colors.neutral[200],
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: Spacing.xs,
  },
  goalFill: {
    height: '100%',
    backgroundColor: Colors.student.secondary,
    borderRadius: 3,
  },
  goalText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  goalCompleted: {
    backgroundColor: Colors.student.success + '20',
    padding: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  goalCompletedText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.student.success,
    textAlign: 'center',
    fontWeight: '600' as const,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  achievementsSection: {
    padding: Spacing.md,
  },
  achievementCard: {
    width: 120,
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginRight: Spacing.sm,
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.neutral[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  achievementLocked: {
    opacity: 0.5,
  },
  achievementIcon: {
    fontSize: Typography.fontSize['2xl'],
    marginBottom: Spacing.xs,
  },
  achievementTitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  achievementPoints: {
    fontSize: Typography.fontSize.xs,
    color: Colors.student.primary,
    fontWeight: '600' as const,
  },
  achievementProgress: {
    width: '100%',
    marginTop: Spacing.xs,
  },
  achievementProgressBar: {
    height: 4,
    backgroundColor: Colors.neutral[200],
    borderRadius: 2,
    overflow: 'hidden',
  },
  achievementProgressFill: {
    height: '100%',
    backgroundColor: Colors.student.primary,
    borderRadius: 2,
  },
  achievementProgressText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.tertiary,
    textAlign: 'center',
    marginTop: Spacing.xs / 2,
  },
  badgesSection: {
    padding: Spacing.md,
  },
  badgesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  badgeCard: {
    width: (width - Spacing.md * 2 - Spacing.sm * 2) / 3,
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    alignItems: 'center',
    borderWidth: 2,
    elevation: 1,
  },
  badgeIcon: {
    fontSize: Typography.fontSize.xl,
    marginBottom: Spacing.xs / 2,
  },
  badgeName: {
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.xs / 2,
  },
  rarityIndicator: {
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  rarityText: {
    fontSize: 8,
    color: Colors.text.inverse,
    fontWeight: '700' as const,
  },
  rewardsSection: {
    padding: Spacing.md,
  },
  rewardsList: {
    gap: Spacing.sm,
  },
  rewardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    elevation: 1,
  },
  rewardIcon: {
    fontSize: Typography.fontSize.lg,
    marginRight: Spacing.sm,
  },
  rewardText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    flex: 1,
  },
  noRewardsText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.tertiary,
    textAlign: 'center',
    fontStyle: 'italic',
    padding: Spacing.lg,
  },
});

export default GamificationDashboard;
