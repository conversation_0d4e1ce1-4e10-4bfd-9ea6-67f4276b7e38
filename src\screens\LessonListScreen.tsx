import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, FlatList, TouchableOpacity, Alert } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { loadLessons, Lesson, clearAllData } from '../services/StorageService'; // Import clearAllData for testing

type LessonListScreenProps = NativeStackScreenProps<RootStackParamList, 'LessonList'>;

const LessonListScreen = ({ navigation }: LessonListScreenProps) => {
  const [lessons, setLessons] = useState<Lesson[]>([]);

  const fetchLessons = async () => {
    const storedLessons = await loadLessons();
    setLessons(storedLessons);
  };

  useEffect(() => {
    // Fetch lessons when the component mounts and when the screen is focused
    const unsubscribe = navigation.addListener('focus', () => {
      fetchLessons();
    });
    return unsubscribe;
  }, [navigation]);

  const handleCreateNewLesson = () => {
    navigation.navigate('ContentEditor', {}); // Navigate without a lessonId to create a new one
  };

  const handleEditLesson = (lessonId: string) => {
    navigation.navigate('ContentEditor', { lessonId: lessonId }); // Pass lessonId to edit existing lesson
  };

  const handleDeleteLesson = (lessonId: string) => {
    Alert.alert(
      "Delete Lesson",
      "Are you sure you want to delete this lesson?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          onPress: async () => {
            const updatedLessons = lessons.filter(lesson => lesson.id !== lessonId);
            // In a real app, you'd update storage here
            // For now, let's just update local state and re-save (need to implement saveLessons in this component)
            // Or, ideally, modify StorageService to include a delete function
            // For this example, let's just update local state for demonstration
            // To make it persistent, you would typically reload all lessons from storage, modify, and save all.
            // For now, let's just alert and not actually delete from storage to keep it simple.
            Alert.alert("Delete", "Lesson deletion is not yet persistent. Feature coming soon.");
            fetchLessons(); // Re-fetch to show current state
          },
          style: "destructive"
        }
      ]
    );
  };

  const handleClearAllData = async () => {
    Alert.alert(
      "Clear All Data",
      "Are you sure you want to clear ALL stored data (lessons and exercises)? This cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Clear All",
          onPress: async () => {
            await clearAllData();
            fetchLessons(); // Refresh lessons after clearing
            Alert.alert("Data Cleared", "All local data has been removed.");
          },
          style: "destructive"
        }
      ]
    );
  };

  const renderItem = ({ item }: { item: Lesson }) => (
    <View style={styles.lessonItem}>
      <Text style={styles.lessonTitle}>{item.title || 'Untitled Lesson'}</Text>
      <Text style={styles.lessonDescription}>{item.description || 'No description'}</Text>
      <View style={styles.lessonActions}>
        <Button title="Edit" onPress={() => handleEditLesson(item.id)} />
        <View style={{ width: 10 }} />
        <Button title="Delete" onPress={() => handleDeleteLesson(item.id)} color="red" />
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>My Lessons</Text>
      <Button title="Create New Lesson" onPress={handleCreateNewLesson} />
      <View style={{ height: 20 }} />
      {lessons.length === 0 ? (
        <Text>No lessons created yet. Start by creating a new one!</Text>
      ) : (
        <FlatList
          data={lessons}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          style={styles.lessonList}
        />
      )}
      <View style={{ height: 20 }} />
      <Button title="Clear All Stored Data (Dev Only)" onPress={handleClearAllData} color="orange" />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8f8f8',
  },
  header: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  lessonList: {
    marginTop: 10,
    width: '100%',
  },
  lessonItem: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eee',
  },
  lessonTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  lessonDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
    marginBottom: 10,
  },
  lessonActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
});

export default LessonListScreen;
