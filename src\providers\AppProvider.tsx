import React, { useEffect, useState } from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { store, persistor } from '../store';
import { setOnlineStatus } from '../store/slices/offlineSlice';
import { setSession, getCurrentSession } from '../store/slices/authSlice';
import { supabase } from '../services/supabase';
import { multiDeviceService } from '../services/multiDeviceService';
import { Colors, Typography, Spacing } from '../constants/theme';

interface AppProviderProps {
  children: React.ReactNode;
}

const LoadingScreen = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={Colors.primary[500]} />
    <Text style={styles.loadingText}>Loading your learning journey...</Text>
  </View>
);

const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Set up network monitoring
      const unsubscribeNetInfo = NetInfo.addEventListener(state => {
        store.dispatch(setOnlineStatus(state.isConnected ?? false));
      });

      // Set up authentication state listener
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          console.log('Auth state changed:', event, session?.user?.email);
          store.dispatch(setSession(session));
          
          if (session) {
            // User signed in - initialize multi-device service and fetch user data
            try {
              await multiDeviceService.initialize();
              console.log('User signed in, fetching data...');
            } catch (error) {
              console.error('Error fetching user data:', error);
            }
          } else {
            // User signed out - clear data and cleanup
            multiDeviceService.cleanup();
            console.log('User signed out, clearing data...');
          }
        }
      );

      // Check current session
      store.dispatch(getCurrentSession());

      setIsInitialized(true);

      // Cleanup function
      return () => {
        unsubscribeNetInfo();
        subscription?.unsubscribe();
      };
    } catch (error) {
      console.error('Error initializing app:', error);
      setIsInitialized(true); // Still allow app to load
    }
  };

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingScreen />} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
    padding: Spacing.xl,
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    color: Colors.text.secondary,
    marginTop: Spacing.lg,
    textAlign: 'center',
  },
});

export default AppProvider;
