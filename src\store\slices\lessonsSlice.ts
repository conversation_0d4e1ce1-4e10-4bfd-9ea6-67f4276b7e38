import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { supabase, Tables } from '../../services/supabase';

interface LessonsState {
  lessons: Tables<'lessons'>[];
  loading: boolean;
  error: string | null;
}

const initialState: LessonsState = {
  lessons: [],
  loading: false,
  error: null,
};

export const fetchLessons = createAsyncThunk(
  'lessons/fetchLessons',
  async (teacherId: string, { rejectWithValue }) => {
    try {
      const { data, error } = await supabase
        .from('lessons')
        .select('*')
        .eq('teacher_id', teacherId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const lessonsSlice = createSlice({
  name: 'lessons',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchLessons.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchLessons.fulfilled, (state, action) => {
        state.loading = false;
        state.lessons = action.payload;
      })
      .addCase(fetchLessons.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default lessonsSlice.reducer;
