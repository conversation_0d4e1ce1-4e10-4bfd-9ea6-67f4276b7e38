import { supabase } from './supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

interface DeviceSession {
  id: string;
  device_id: string;
  device_name: string;
  device_type: string;
  platform: string;
  app_version: string;
  last_active: string;
  user_id: string;
  is_current: boolean;
}

interface SyncData {
  lastSyncTime: number;
  pendingActions: any[];
  offlineData: any;
}

class MultiDeviceService {
  private deviceId: string | null = null;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL = 30000; // 30 seconds
  private readonly DEVICE_ID_KEY = 'device_id';
  
  async initialize() {
    try {
      // Get or create device ID
      this.deviceId = await this.getOrCreateDeviceId();
      
      console.log('Multi-device service initialized for device:', this.deviceId, '(Mock mode)');
      
      // Skip database operations in mock mode
      // Register current session
      // await this.registerDeviceSession();
      
      // Start sync timer
      // this.startSyncTimer();
      
    } catch (error) {
      console.error('Failed to initialize multi-device service:', error);
    }
  }

  private async getOrCreateDeviceId(): Promise<string> {
    try {
      // Try to get existing device ID
      let deviceId = await AsyncStorage.getItem(this.DEVICE_ID_KEY);
      
      if (!deviceId) {
        // Create new device ID using device info
        const uniqueId = await DeviceInfo.getUniqueId();
        const timestamp = Date.now();
        deviceId = `${Platform.OS}_${uniqueId}_${timestamp}`;
        
        await AsyncStorage.setItem(this.DEVICE_ID_KEY, deviceId);
      }
      
      return deviceId;
    } catch (error) {
      console.error('Error getting/creating device ID:', error);
      // Fallback to random ID
      return `${Platform.OS}_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`;
    }
  }

  private async registerDeviceSession() {
    if (!this.deviceId) return;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const deviceInfo = await this.getDeviceInfo();
      
      // First, mark all other sessions for this user as not current
      await supabase
        .from('device_sessions')
        .update({ is_current: false })
        .eq('user_id', user.id);

      // Register or update current device session
      const { error } = await supabase
        .from('device_sessions')
        .upsert({
          device_id: this.deviceId,
          user_id: user.id,
          device_name: deviceInfo.deviceName,
          device_type: deviceInfo.deviceType,
          platform: deviceInfo.platform,
          app_version: deviceInfo.appVersion,
          last_active: new Date().toISOString(),
          is_current: true,
        }, {
          onConflict: 'device_id'
        });

      if (error) {
        console.error('Error registering device session:', error);
      } else {
        console.log('Device session registered successfully');
      }
    } catch (error) {
      console.error('Failed to register device session:', error);
    }
  }

  private async getDeviceInfo() {
    try {
      const [deviceName, systemName, systemVersion, buildNumber, version] = await Promise.all([
        DeviceInfo.getDeviceName(),
        DeviceInfo.getSystemName(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getBuildNumber(),
        DeviceInfo.getVersion(),
      ]);

      return {
        deviceName,
        deviceType: await DeviceInfo.getDeviceType(),
        platform: `${systemName} ${systemVersion}`,
        appVersion: `${version} (${buildNumber})`,
      };
    } catch (error) {
      console.error('Error getting device info:', error);
      return {
        deviceName: 'Unknown Device',
        deviceType: 'unknown',
        platform: Platform.OS,
        appVersion: '1.0.0',
      };
    }
  }

  private startSyncTimer() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      await this.syncData();
      await this.updateLastActive();
    }, this.SYNC_INTERVAL);
  }

  private async updateLastActive() {
    if (!this.deviceId) return;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      await supabase
        .from('device_sessions')
        .update({ 
          last_active: new Date().toISOString() 
        })
        .eq('device_id', this.deviceId)
        .eq('user_id', user.id);
    } catch (error) {
      console.error('Error updating last active:', error);
    }
  }

  async syncData() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get last sync time
      const lastSyncTime = await this.getLastSyncTime();
      
      // Fetch data that changed since last sync
      const changedData = await this.fetchChangedData(user.id, lastSyncTime);
      
      // Apply changes locally
      await this.applyChanges(changedData);
      
      // Upload pending local changes
      await this.uploadPendingChanges(user.id);
      
      // Update last sync time
      await this.setLastSyncTime(Date.now());
      
      console.log('Data sync completed successfully');
    } catch (error) {
      console.error('Error during data sync:', error);
    }
  }

  private async getLastSyncTime(): Promise<number> {
    try {
      const syncData = await AsyncStorage.getItem('last_sync_time');
      return syncData ? parseInt(syncData, 10) : 0;
    } catch (error) {
      console.error('Error getting last sync time:', error);
      return 0;
    }
  }

  private async setLastSyncTime(timestamp: number) {
    try {
      await AsyncStorage.setItem('last_sync_time', timestamp.toString());
    } catch (error) {
      console.error('Error setting last sync time:', error);
    }
  }

  private async fetchChangedData(userId: string, lastSyncTime: number) {
    const lastSyncDate = new Date(lastSyncTime).toISOString();
    
    const [lessonsData, exercisesData, progressData, achievementsData] = await Promise.all([
      // Fetch lessons updated since last sync
      supabase
        .from('lessons')
        .select('*')
        .eq('teacher_id', userId)
        .gt('updated_at', lastSyncDate),
      
      // Fetch exercises updated since last sync
      supabase
        .from('exercises')
        .select('*')
        .eq('teacher_id', userId)
        .gt('updated_at', lastSyncDate),
      
      // Fetch progress updated since last sync
      supabase
        .from('student_progress')
        .select(`
          *,
          students!inner(user_id)
        `)
        .eq('students.user_id', userId)
        .gt('updated_at', lastSyncDate),
      
      // Fetch achievements earned since last sync
      supabase
        .from('achievements')
        .select(`
          *,
          students!inner(user_id)
        `)
        .eq('students.user_id', userId)
        .gt('created_at', lastSyncDate),
    ]);

    return {
      lessons: lessonsData.data || [],
      exercises: exercisesData.data || [],
      progress: progressData.data || [],
      achievements: achievementsData.data || [],
    };
  }

  private async applyChanges(changedData: any) {
    // Store changed data in AsyncStorage for offline access
    try {
      if (changedData.lessons.length > 0) {
        const existingLessons = await AsyncStorage.getItem('cached_lessons');
        const lessons = existingLessons ? JSON.parse(existingLessons) : [];
        
        // Merge or update lessons
        changedData.lessons.forEach((newLesson: any) => {
          const index = lessons.findIndex((l: any) => l.id === newLesson.id);
          if (index >= 0) {
            lessons[index] = newLesson;
          } else {
            lessons.push(newLesson);
          }
        });
        
        await AsyncStorage.setItem('cached_lessons', JSON.stringify(lessons));
      }

      // Similar logic for exercises, progress, and achievements
      // This ensures offline access to the most recent data
      
      console.log('Applied changes locally:', {
        lessons: changedData.lessons.length,
        exercises: changedData.exercises.length,
        progress: changedData.progress.length,
        achievements: changedData.achievements.length,
      });
    } catch (error) {
      console.error('Error applying changes locally:', error);
    }
  }

  private async uploadPendingChanges(userId: string) {
    try {
      const pendingActions = await AsyncStorage.getItem('pending_sync_actions');
      if (!pendingActions) return;

      const actions = JSON.parse(pendingActions);
      const completedActions: string[] = [];

      for (const action of actions) {
        try {
          await this.executeAction(action);
          completedActions.push(action.id);
        } catch (error) {
          console.error('Failed to execute action:', action, error);
        }
      }

      // Remove completed actions
      const remainingActions = actions.filter(
        (action: any) => !completedActions.includes(action.id)
      );
      
      await AsyncStorage.setItem('pending_sync_actions', JSON.stringify(remainingActions));
      
      console.log(`Uploaded ${completedActions.length} pending changes`);
    } catch (error) {
      console.error('Error uploading pending changes:', error);
    }
  }

  private async executeAction(action: any) {
    switch (action.type) {
      case 'update_progress':
        await supabase
          .from('student_progress')
          .upsert(action.data);
        break;
      
      case 'create_lesson':
        await supabase
          .from('lessons')
          .insert(action.data);
        break;
      
      case 'update_lesson':
        await supabase
          .from('lessons')
          .update(action.data)
          .eq('id', action.data.id);
        break;
      
      // Add more action types as needed
      default:
        console.warn('Unknown action type:', action.type);
    }
  }

  async addPendingAction(actionType: string, data: any) {
    try {
      const action = {
        id: `${actionType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: actionType,
        data,
        timestamp: Date.now(),
      };

      const pendingActions = await AsyncStorage.getItem('pending_sync_actions');
      const actions = pendingActions ? JSON.parse(pendingActions) : [];
      actions.push(action);
      
      await AsyncStorage.setItem('pending_sync_actions', JSON.stringify(actions));
      
      console.log('Added pending action:', actionType);
    } catch (error) {
      console.error('Error adding pending action:', error);
    }
  }

  async getActiveDevices(userId: string): Promise<DeviceSession[]> {
    try {
      const { data, error } = await supabase
        .from('device_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('last_active', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching active devices:', error);
      return [];
    }
  }

  async signOutFromDevice(deviceId: string) {
    try {
      const { error } = await supabase
        .from('device_sessions')
        .delete()
        .eq('device_id', deviceId);

      if (error) throw error;
      
      console.log('Signed out from device:', deviceId);
    } catch (error) {
      console.error('Error signing out from device:', error);
    }
  }

  async signOutFromAllDevices(userId: string) {
    try {
      // Keep only current device session
      const { error } = await supabase
        .from('device_sessions')
        .delete()
        .eq('user_id', userId)
        .neq('device_id', this.deviceId);

      if (error) throw error;
      
      console.log('Signed out from all other devices');
    } catch (error) {
      console.error('Error signing out from all devices:', error);
    }
  }

  cleanup() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }
}

export const multiDeviceService = new MultiDeviceService();
export default MultiDeviceService;
