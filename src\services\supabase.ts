import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Development/Mock configuration
// For production, replace these with your actual Supabase credentials
const SUPABASE_URL = 'https://mock-project.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1vY2stcHJvamVjdCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjQ1OTk3ODk2LCJleHAiOjE5NjE1NzM4OTZ9';

// Mock Supabase client for development
const createMockClient = () => {
  return {
    auth: {
      onAuthStateChange: (callback: any) => {
        // Mock auth state - simulate no user initially
        setTimeout(() => callback('SIGNED_OUT', null), 100);
        return {
          data: {
            subscription: {
              unsubscribe: () => console.log('Mock auth unsubscribed')
            }
          }
        };
      },
      getSession: async () => ({ data: { session: null }, error: null }),
      signUp: async () => ({ data: { user: null, session: null }, error: { message: 'Mock mode - authentication disabled' } }),
      signIn: async () => ({ data: { user: null, session: null }, error: { message: 'Mock mode - authentication disabled' } }),
      signOut: async () => ({ error: null })
    },
    from: (table: string) => ({
      select: () => ({
        eq: () => ({ data: [], error: null }),
        data: [],
        error: null
      }),
      insert: () => ({ data: null, error: null }),
      update: () => ({ data: null, error: null }),
      delete: () => ({ data: null, error: null })
    })
  };
};

// Use mock client for development
export const supabase = createMockClient() as any;

// Database Types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          role: 'teacher' | 'student' | 'parent';
          full_name: string;
          avatar_url?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          role: 'teacher' | 'student' | 'parent';
          full_name: string;
          avatar_url?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          role?: 'teacher' | 'student' | 'parent';
          full_name?: string;
          avatar_url?: string;
          updated_at?: string;
        };
      };
      teachers: {
        Row: {
          id: string;
          user_id: string;
          school_name?: string;
          grade_levels: string[];
          subjects: string[];
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          school_name?: string;
          grade_levels?: string[];
          subjects?: string[];
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          school_name?: string;
          grade_levels?: string[];
          subjects?: string[];
          updated_at?: string;
        };
      };
      students: {
        Row: {
          id: string;
          user_id: string;
          teacher_id: string;
          grade_level: string;
          date_of_birth?: string;
          parent_email?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          teacher_id: string;
          grade_level: string;
          date_of_birth?: string;
          parent_email?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          teacher_id?: string;
          grade_level?: string;
          date_of_birth?: string;
          parent_email?: string;
          updated_at?: string;
        };
      };
      lessons: {
        Row: {
          id: string;
          teacher_id: string;
          title: string;
          description?: string;
          subject: string;
          grade_level: string;
          difficulty: 'Easy' | 'Medium' | 'Hard';
          content: any;
          estimated_duration: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          teacher_id: string;
          title: string;
          description?: string;
          subject: string;
          grade_level: string;
          difficulty: 'Easy' | 'Medium' | 'Hard';
          content?: any;
          estimated_duration?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          title?: string;
          description?: string;
          subject?: string;
          grade_level?: string;
          difficulty?: 'Easy' | 'Medium' | 'Hard';
          content?: any;
          estimated_duration?: number;
          updated_at?: string;
        };
      };
      exercises: {
        Row: {
          id: string;
          teacher_id: string;
          lesson_id?: string;
          title: string;
          instructions: string;
          type: 'multiple_choice' | 'drag_drop' | 'fill_blank' | 'matching' | 'drawing' | 'audio';
          content: any;
          difficulty: 'Easy' | 'Medium' | 'Hard';
          subject: string;
          grade_level: string;
          points: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          teacher_id: string;
          lesson_id?: string;
          title: string;
          instructions: string;
          type: 'multiple_choice' | 'drag_drop' | 'fill_blank' | 'matching' | 'drawing' | 'audio';
          content?: any;
          difficulty: 'Easy' | 'Medium' | 'Hard';
          subject: string;
          grade_level: string;
          points?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          lesson_id?: string;
          title?: string;
          instructions?: string;
          type?: 'multiple_choice' | 'drag_drop' | 'fill_blank' | 'matching' | 'drawing' | 'audio';
          content?: any;
          difficulty?: 'Easy' | 'Medium' | 'Hard';
          subject?: string;
          grade_level?: string;
          points?: number;
          updated_at?: string;
        };
      };
      student_progress: {
        Row: {
          id: string;
          student_id: string;
          lesson_id?: string;
          exercise_id?: string;
          status: 'not_started' | 'in_progress' | 'completed';
          score?: number;
          time_spent: number;
          attempts: number;
          last_attempt_at?: string;
          completed_at?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          student_id: string;
          lesson_id?: string;
          exercise_id?: string;
          status?: 'not_started' | 'in_progress' | 'completed';
          score?: number;
          time_spent?: number;
          attempts?: number;
          last_attempt_at?: string;
          completed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          status?: 'not_started' | 'in_progress' | 'completed';
          score?: number;
          time_spent?: number;
          attempts?: number;
          last_attempt_at?: string;
          completed_at?: string;
          updated_at?: string;
        };
      };
      achievements: {
        Row: {
          id: string;
          student_id: string;
          type: string;
          title: string;
          description: string;
          icon: string;
          points: number;
          earned_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          student_id: string;
          type: string;
          title: string;
          description: string;
          icon: string;
          points?: number;
          earned_at?: string;
          created_at?: string;
        };
        Update: {
          type?: string;
          title?: string;
          description?: string;
          icon?: string;
          points?: number;
        };
      };
    };
  };
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];

// Export typed client
export default supabase;
