import AsyncStorage from '@react-native-async-storage/async-storage';

const LESSONS_KEY = 'lessons';
const EXERCISES_KEY = 'exercises';

export interface Lesson {
  id: string;
  title: string;
  description: string;
  contentSections: { id: string; type: 'text' | 'image' | 'video' | 'audio'; value: string }[];
  exerciseIds: string[]; // IDs of exercises associated with this lesson
}

export interface MultipleChoiceQuestion {
  id: string;
  type: 'multiple-choice';
  questionText: string;
  options: { id: string; text: string }[];
  correctAnswerId: string;
}

// Define the structure of a complete Exercise object
export interface Exercise {
  id: string;
  exerciseTitle: string;
  exerciseDescription: string;
  questions: MultipleChoiceQuestion[]; // Array of questions for this exercise
}

// Lessons
export const saveLessons = async (lessons: Lesson[]): Promise<void> => {
  try {
    const jsonValue = JSON.stringify(lessons);
    await AsyncStorage.setItem(LESSONS_KEY, jsonValue);
  } catch (e) {
    console.error('Error saving lessons:', e);
  }
};

export const loadLessons = async (): Promise<Lesson[]> => {
  try {
    const jsonValue = await AsyncStorage.getItem(LESSONS_KEY);
    return jsonValue != null ? JSON.parse(jsonValue) : [];
  } catch (e) {
    console.error('Error loading lessons:', e);
    return [];
  }
};

// Exercises
export const saveExercises = async (exercises: Exercise[]): Promise<void> => {
  try {
    const jsonValue = JSON.stringify(exercises);
    await AsyncStorage.setItem(EXERCISES_KEY, jsonValue);
  } catch (e) {
    console.error('Error saving exercises:', e);
  }
};

export const loadExercises = async (): Promise<Exercise[]> => {
  try {
    const jsonValue = await AsyncStorage.getItem(EXERCISES_KEY);
    return jsonValue != null ? JSON.parse(jsonValue) : [];
  } catch (e) {
    console.error('Error loading exercises:', e);
    return [];
  }
};

// Clear all data (for development/testing)
export const clearAllData = async (): Promise<void> => {
  try {
    await AsyncStorage.clear();
    console.log('All AsyncStorage data cleared.');
  } catch (e) {
    console.error('Error clearing all data:', e);
  }
};
