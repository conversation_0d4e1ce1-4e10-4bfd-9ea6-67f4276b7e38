import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  PanResponder,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

interface DragItem {
  id: string;
  text: string;
  category: string;
  color?: string;
}

interface DropZone {
  id: string;
  label: string;
  acceptedCategory: string;
  color: string;
}

interface DragDropExerciseProps {
  items: DragItem[];
  dropZones: DropZone[];
  onComplete: (results: { [zoneId: string]: DragItem[] }) => void;
  onItemDrop?: (item: DragItem, zone: DropZone) => void;
  instruction?: string;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const DragDropExercise: React.FC<DragDropExerciseProps> = ({
  items,
  dropZones,
  onComplete,
  onItemDrop,
  instruction = "Drag items to their correct categories"
}) => {
  const [droppedItems, setDroppedItems] = useState<{ [zoneId: string]: DragItem[] }>({});
  const [availableItems, setAvailableItems] = useState<DragItem[]>(items);
  const [draggedItem, setDraggedItem] = useState<DragItem | null>(null);
  
  // Animation values for each item
  const animatedValues = useRef(
    availableItems.reduce((acc, item) => {
      acc[item.id] = {
        pan: new Animated.ValueXY(),
        scale: new Animated.Value(1),
        zIndex: new Animated.Value(1),
      };
      return acc;
    }, {} as any)
  ).current;

  const createPanResponder = (item: DragItem) => {
    return PanResponder.create({
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        setDraggedItem(item);
        Animated.parallel([
          Animated.spring(animatedValues[item.id].scale, {
            toValue: 1.1,
            useNativeDriver: false,
          }),
          Animated.spring(animatedValues[item.id].zIndex, {
            toValue: 1000,
            useNativeDriver: false,
          }),
        ]).start();
      },
      onPanResponderMove: Animated.event(
        [null, { dx: animatedValues[item.id].pan.x, dy: animatedValues[item.id].pan.y }],
        { useNativeDriver: false }
      ),
      onPanResponderRelease: (evt, gesture) => {
        const dropZone = findDropZone(gesture.moveX, gesture.moveY);
        
        if (dropZone) {
          handleDrop(item, dropZone);
        } else {
          // Snap back to original position
          Animated.parallel([
            Animated.spring(animatedValues[item.id].pan, {
              toValue: { x: 0, y: 0 },
              useNativeDriver: false,
            }),
            Animated.spring(animatedValues[item.id].scale, {
              toValue: 1,
              useNativeDriver: false,
            }),
            Animated.spring(animatedValues[item.id].zIndex, {
              toValue: 1,
              useNativeDriver: false,
            }),
          ]).start();
        }
        
        setDraggedItem(null);
      },
    });
  };

  const findDropZone = (x: number, y: number): DropZone | null => {
    // This would need to be calculated based on actual drop zone positions
    // For now, we'll use a simplified approach
    const zoneHeight = 120;
    const zoneY = screenHeight * 0.6; // Approximate position of drop zones
    
    if (y >= zoneY && y <= zoneY + zoneHeight) {
      const zoneWidth = screenWidth / dropZones.length;
      const zoneIndex = Math.floor(x / zoneWidth);
      return dropZones[zoneIndex] || null;
    }
    
    return null;
  };

  const handleDrop = (item: DragItem, zone: DropZone) => {
    // Check if item belongs to this category
    const isCorrect = item.category === zone.acceptedCategory;
    
    if (isCorrect) {
      // Add item to drop zone
      setDroppedItems(prev => ({
        ...prev,
        [zone.id]: [...(prev[zone.id] || []), item]
      }));
      
      // Remove from available items
      setAvailableItems(prev => prev.filter(i => i.id !== item.id));
      
      // Reset animation
      Animated.parallel([
        Animated.spring(animatedValues[item.id].pan, {
          toValue: { x: 0, y: 0 },
          useNativeDriver: false,
        }),
        Animated.spring(animatedValues[item.id].scale, {
          toValue: 1,
          useNativeDriver: false,
        }),
        Animated.spring(animatedValues[item.id].zIndex, {
          toValue: 1,
          useNativeDriver: false,
        }),
      ]).start();
      
      // Callback for individual drops
      onItemDrop?.(item, zone);
      
      // Check if exercise is complete
      if (availableItems.length === 1) {
        setTimeout(() => {
          onComplete({
            ...droppedItems,
            [zone.id]: [...(droppedItems[zone.id] || []), item]
          });
        }, 500);
      }
    } else {
      // Wrong category - bounce back with feedback
      Animated.sequence([
        Animated.spring(animatedValues[item.id].scale, {
          toValue: 1.2,
          useNativeDriver: false,
        }),
        Animated.spring(animatedValues[item.id].pan, {
          toValue: { x: 0, y: 0 },
          useNativeDriver: false,
        }),
        Animated.spring(animatedValues[item.id].scale, {
          toValue: 1,
          useNativeDriver: false,
        }),
        Animated.spring(animatedValues[item.id].zIndex, {
          toValue: 1,
          useNativeDriver: false,
        }),
      ]).start();
    }
  };

  const renderDragItem = (item: DragItem) => {
    const panResponder = createPanResponder(item);
    
    return (
      <Animated.View
        key={item.id}
        style={[
          styles.dragItem,
          {
            backgroundColor: item.color || Colors.student.primary,
            transform: [
              { translateX: animatedValues[item.id].pan.x },
              { translateY: animatedValues[item.id].pan.y },
              { scale: animatedValues[item.id].scale },
            ],
            zIndex: animatedValues[item.id].zIndex,
          },
        ]}
        {...panResponder.panHandlers}
      >
        <Text style={styles.dragItemText}>{item.text}</Text>
      </Animated.View>
    );
  };

  const renderDropZone = (zone: DropZone) => {
    const itemsInZone = droppedItems[zone.id] || [];
    
    return (
      <View
        key={zone.id}
        style={[
          styles.dropZone,
          { 
            backgroundColor: zone.color + '20',
            borderColor: zone.color,
          }
        ]}
      >
        <Text style={[styles.dropZoneLabel, { color: zone.color }]}>
          {zone.label}
        </Text>
        
        <View style={styles.droppedItemsContainer}>
          {itemsInZone.map((item) => (
            <View
              key={item.id}
              style={[
                styles.droppedItem,
                { backgroundColor: item.color || Colors.student.primary }
              ]}
            >
              <Text style={styles.droppedItemText}>{item.text}</Text>
            </View>
          ))}
        </View>
        
        {itemsInZone.length === 0 && (
          <View style={styles.emptyZonePlaceholder}>
            <Text style={styles.emptyZoneText}>Drop items here</Text>
          </View>
        )}
      </View>
    );
  };

  const resetExercise = () => {
    setAvailableItems(items);
    setDroppedItems({});
    setDraggedItem(null);
    
    // Reset all animations
    Object.keys(animatedValues).forEach(itemId => {
      animatedValues[itemId].pan.setValue({ x: 0, y: 0 });
      animatedValues[itemId].scale.setValue(1);
      animatedValues[itemId].zIndex.setValue(1);
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.instructionContainer}>
        <Text style={styles.instruction}>{instruction}</Text>
      </View>
      
      <View style={styles.dragItemsContainer}>
        <Text style={styles.sectionTitle}>Items to Sort:</Text>
        <View style={styles.itemsGrid}>
          {availableItems.map(renderDragItem)}
        </View>
      </View>
      
      <View style={styles.dropZonesContainer}>
        <Text style={styles.sectionTitle}>Categories:</Text>
        <View style={styles.zonesGrid}>
          {dropZones.map(renderDropZone)}
        </View>
      </View>
      
      <TouchableOpacity style={styles.resetButton} onPress={resetExercise}>
        <Text style={styles.resetButtonText}>🔄 Reset</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
    backgroundColor: Colors.background.secondary,
  },
  instructionContainer: {
    backgroundColor: Colors.background.primary,
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.lg,
    elevation: 2,
  },
  instruction: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  dragItemsContainer: {
    marginBottom: Spacing.xl,
  },
  itemsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  dragItem: {
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    elevation: 3,
    shadowColor: Colors.neutral[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dragItemText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '600' as const,
    color: Colors.text.inverse,
    textAlign: 'center',
  },
  dropZonesContainer: {
    flex: 1,
  },
  zonesGrid: {
    flexDirection: 'row',
    gap: Spacing.sm,
    flex: 1,
  },
  dropZone: {
    flex: 1,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    minHeight: 120,
    alignItems: 'center',
  },
  dropZoneLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: '700' as const,
    marginBottom: Spacing.sm,
  },
  droppedItemsContainer: {
    flex: 1,
    width: '100%',
    gap: Spacing.xs,
  },
  droppedItem: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.xs,
  },
  droppedItemText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.inverse,
    textAlign: 'center',
    fontWeight: '500' as const,
  },
  emptyZonePlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyZoneText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.tertiary,
    fontStyle: 'italic',
  },
  resetButton: {
    backgroundColor: Colors.student.secondary,
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  resetButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600' as const,
    color: Colors.text.inverse,
  },
});

export default DragDropExercise;
