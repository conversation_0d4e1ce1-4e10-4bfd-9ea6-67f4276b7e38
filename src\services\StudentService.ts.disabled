import AsyncStorage from '@react-native-async-storage/async-storage';
import { Student, StudentProgress, StudentGrade, StudentAttendance, StudentFilters, StudentSortField, SortDirection } from '../types/Student';

const STUDENTS_KEY = 'students';
const STUDENT_PROGRESS_KEY = 'student_progress';
const STUDENT_GRADES_KEY = 'student_grades';
const STUDENT_ATTENDANCE_KEY = 'student_attendance';

export class StudentService {
  // Student CRUD Operations
  static async getAllStudents(): Promise<Student[]> {
    try {
      const studentsJson = await AsyncStorage.getItem(STUDENTS_KEY);
      return studentsJson ? JSON.parse(studentsJson) : [];
    } catch (error) {
      console.error('Error loading students:', error);
      return [];
    }
  }

  static async getStudentById(id: string): Promise<Student | null> {
    try {
      const students = await this.getAllStudents();
      return students.find(student => student.id === id) || null;
    } catch (error) {
      console.error('Error loading student:', error);
      return null;
    }
  }

  static async saveStudent(student: Student): Promise<void> {
    try {
      const students = await this.getAllStudents();
      const existingIndex = students.findIndex(s => s.id === student.id);
      
      if (existingIndex >= 0) {
        students[existingIndex] = student;
      } else {
        students.push(student);
      }
      
      await AsyncStorage.setItem(STUDENTS_KEY, JSON.stringify(students));
    } catch (error) {
      console.error('Error saving student:', error);
      throw error;
    }
  }

  static async deleteStudent(id: string): Promise<void> {
    try {
      const students = await this.getAllStudents();
      const filteredStudents = students.filter(student => student.id !== id);
      await AsyncStorage.setItem(STUDENTS_KEY, JSON.stringify(filteredStudents));
      
      // Also clean up related data
      await this.deleteStudentProgress(id);
      await this.deleteStudentGrades(id);
      await this.deleteStudentAttendance(id);
    } catch (error) {
      console.error('Error deleting student:', error);
      throw error;
    }
  }

  // Filtering and Sorting
  static async getFilteredStudents(
    filters: StudentFilters,
    sortField: StudentSortField = 'firstName',
    sortDirection: SortDirection = 'asc'
  ): Promise<Student[]> {
    try {
      let students = await this.getAllStudents();

      // Apply filters
      if (filters.grade) {
        students = students.filter(s => s.grade === filters.grade);
      }
      if (filters.section) {
        students = students.filter(s => s.section === filters.section);
      }
      if (filters.status) {
        students = students.filter(s => s.status === filters.status);
      }
      if (filters.subject) {
        students = students.filter(s => s.subjects.includes(filters.subject));
      }
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        students = students.filter(s => 
          s.firstName.toLowerCase().includes(searchLower) ||
          s.lastName.toLowerCase().includes(searchLower) ||
          s.email?.toLowerCase().includes(searchLower) ||
          s.parentName?.toLowerCase().includes(searchLower)
        );
      }

      // Apply sorting
      students.sort((a, b) => {
        let aValue: any = a[sortField];
        let bValue: any = b[sortField];

        if (sortField === 'enrollmentDate' || sortField === 'lastActivity') {
          aValue = new Date(aValue || 0);
          bValue = new Date(bValue || 0);
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });

      return students;
    } catch (error) {
      console.error('Error filtering students:', error);
      return [];
    }
  }

  // Progress Management
  static async getStudentProgress(studentId: string): Promise<StudentProgress[]> {
    try {
      const progressJson = await AsyncStorage.getItem(STUDENT_PROGRESS_KEY);
      const allProgress: StudentProgress[] = progressJson ? JSON.parse(progressJson) : [];
      return allProgress.filter(p => p.studentId === studentId);
    } catch (error) {
      console.error('Error loading student progress:', error);
      return [];
    }
  }

  static async saveStudentProgress(progress: StudentProgress): Promise<void> {
    try {
      const progressJson = await AsyncStorage.getItem(STUDENT_PROGRESS_KEY);
      const allProgress: StudentProgress[] = progressJson ? JSON.parse(progressJson) : [];
      
      const existingIndex = allProgress.findIndex(p => 
        p.studentId === progress.studentId && p.subject === progress.subject
      );
      
      if (existingIndex >= 0) {
        allProgress[existingIndex] = progress;
      } else {
        allProgress.push(progress);
      }
      
      await AsyncStorage.setItem(STUDENT_PROGRESS_KEY, JSON.stringify(allProgress));
    } catch (error) {
      console.error('Error saving student progress:', error);
      throw error;
    }
  }

  static async deleteStudentProgress(studentId: string): Promise<void> {
    try {
      const progressJson = await AsyncStorage.getItem(STUDENT_PROGRESS_KEY);
      const allProgress: StudentProgress[] = progressJson ? JSON.parse(progressJson) : [];
      const filteredProgress = allProgress.filter(p => p.studentId !== studentId);
      await AsyncStorage.setItem(STUDENT_PROGRESS_KEY, JSON.stringify(filteredProgress));
    } catch (error) {
      console.error('Error deleting student progress:', error);
    }
  }

  // Grades Management
  static async getStudentGrades(studentId: string): Promise<StudentGrade[]> {
    try {
      const gradesJson = await AsyncStorage.getItem(STUDENT_GRADES_KEY);
      const allGrades: StudentGrade[] = gradesJson ? JSON.parse(gradesJson) : [];
      return allGrades.filter(g => g.studentId === studentId);
    } catch (error) {
      console.error('Error loading student grades:', error);
      return [];
    }
  }

  static async saveStudentGrade(grade: StudentGrade): Promise<void> {
    try {
      const gradesJson = await AsyncStorage.getItem(STUDENT_GRADES_KEY);
      const allGrades: StudentGrade[] = gradesJson ? JSON.parse(gradesJson) : [];
      
      const existingIndex = allGrades.findIndex(g => g.id === grade.id);
      
      if (existingIndex >= 0) {
        allGrades[existingIndex] = grade;
      } else {
        allGrades.push(grade);
      }
      
      await AsyncStorage.setItem(STUDENT_GRADES_KEY, JSON.stringify(allGrades));
    } catch (error) {
      console.error('Error saving student grade:', error);
      throw error;
    }
  }

  static async deleteStudentGrades(studentId: string): Promise<void> {
    try {
      const gradesJson = await AsyncStorage.getItem(STUDENT_GRADES_KEY);
      const allGrades: StudentGrade[] = gradesJson ? JSON.parse(gradesJson) : [];
      const filteredGrades = allGrades.filter(g => g.studentId !== studentId);
      await AsyncStorage.setItem(STUDENT_GRADES_KEY, JSON.stringify(filteredGrades));
    } catch (error) {
      console.error('Error deleting student grades:', error);
    }
  }

  // Attendance Management
  static async getStudentAttendance(studentId: string, startDate?: string, endDate?: string): Promise<StudentAttendance[]> {
    try {
      const attendanceJson = await AsyncStorage.getItem(STUDENT_ATTENDANCE_KEY);
      const allAttendance: StudentAttendance[] = attendanceJson ? JSON.parse(attendanceJson) : [];
      let filtered = allAttendance.filter(a => a.studentId === studentId);
      
      if (startDate) {
        filtered = filtered.filter(a => a.date >= startDate);
      }
      if (endDate) {
        filtered = filtered.filter(a => a.date <= endDate);
      }
      
      return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      console.error('Error loading student attendance:', error);
      return [];
    }
  }

  static async saveStudentAttendance(attendance: StudentAttendance): Promise<void> {
    try {
      const attendanceJson = await AsyncStorage.getItem(STUDENT_ATTENDANCE_KEY);
      const allAttendance: StudentAttendance[] = attendanceJson ? JSON.parse(attendanceJson) : [];
      
      const existingIndex = allAttendance.findIndex(a => a.id === attendance.id);
      
      if (existingIndex >= 0) {
        allAttendance[existingIndex] = attendance;
      } else {
        allAttendance.push(attendance);
      }
      
      await AsyncStorage.setItem(STUDENT_ATTENDANCE_KEY, JSON.stringify(allAttendance));
    } catch (error) {
      console.error('Error saving student attendance:', error);
      throw error;
    }
  }

  static async deleteStudentAttendance(studentId: string): Promise<void> {
    try {
      const attendanceJson = await AsyncStorage.getItem(STUDENT_ATTENDANCE_KEY);
      const allAttendance: StudentAttendance[] = attendanceJson ? JSON.parse(attendanceJson) : [];
      const filteredAttendance = allAttendance.filter(a => a.studentId !== studentId);
      await AsyncStorage.setItem(STUDENT_ATTENDANCE_KEY, JSON.stringify(filteredAttendance));
    } catch (error) {
      console.error('Error deleting student attendance:', error);
    }
  }

  // Utility Methods
  static async getStudentStats(): Promise<{
    totalStudents: number;
    activeStudents: number;
    gradeDistribution: Record<string, number>;
    recentEnrollments: number;
  }> {
    try {
      const students = await this.getAllStudents();
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      const gradeDistribution: Record<string, number> = {};
      let recentEnrollments = 0;
      
      students.forEach(student => {
        // Grade distribution
        gradeDistribution[student.grade] = (gradeDistribution[student.grade] || 0) + 1;
        
        // Recent enrollments
        if (new Date(student.enrollmentDate) >= thirtyDaysAgo) {
          recentEnrollments++;
        }
      });
      
      return {
        totalStudents: students.length,
        activeStudents: students.filter(s => s.status === 'active').length,
        gradeDistribution,
        recentEnrollments
      };
    } catch (error) {
      console.error('Error calculating student stats:', error);
      return {
        totalStudents: 0,
        activeStudents: 0,
        gradeDistribution: {},
        recentEnrollments: 0
      };
    }
  }
}
