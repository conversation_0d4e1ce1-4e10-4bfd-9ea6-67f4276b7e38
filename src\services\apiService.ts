import { supabase, Tables, Inserts, Updates } from './supabase';
import { multiDeviceService } from './multiDeviceService';

export interface ClassWithStudents extends Tables<'classes'> {
  students: Tables<'students'>[];
  enrollments: Tables<'class_enrollments'>[];
}

export interface LessonWithExercises extends Tables<'lessons'> {
  exercises: Tables<'exercises'>[];
  class: Tables<'classes'> | null;
}

export interface StudentWithProgress extends Tables<'students'> {
  user: Tables<'users'>;
  progress: Tables<'student_progress'>[];
  achievements: Tables<'achievements'>[];
  learning_streak: Tables<'learning_streaks'> | null;
}

export interface TeacherProfile extends Tables<'teachers'> {
  user: Tables<'users'>;
  classes: Tables<'classes'>[];
  students: Tables<'students'>[];
}

class ApiService {
  // ============= AUTHENTICATION & USER MANAGEMENT =============
  
  async createUserProfile(userData: Inserts<'users'>) {
    try {
      const { data, error } = await supabase
        .from('users')
        .insert(userData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  async createTeacherProfile(userId: string, teacherData: Omit<Inserts<'teachers'>, 'user_id'>) {
    try {
      const { data, error } = await supabase
        .from('teachers')
        .insert({
          ...teacherData,
          user_id: userId,
        })
        .select(`
          *,
          user:users(*)
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating teacher profile:', error);
      throw error;
    }
  }

  async createStudentProfile(userId: string, studentData: Omit<Inserts<'students'>, 'user_id'>) {
    try {
      // Start transaction
      const { data: student, error: studentError } = await supabase
        .from('students')
        .insert({
          ...studentData,
          user_id: userId,
        })
        .select(`
          *,
          user:users(*),
          teacher:teachers(*)
        `)
        .single();

      if (studentError) throw studentError;

      // Create learning streak record
      const { error: streakError } = await supabase
        .from('learning_streaks')
        .insert({
          student_id: student.id,
          current_streak: 0,
          longest_streak: 0,
          total_learning_days: 0,
        });

      if (streakError) console.error('Error creating learning streak:', streakError);

      return student;
    } catch (error) {
      console.error('Error creating student profile:', error);
      throw error;
    }
  }

  // ============= TEACHER OPERATIONS =============

  async getTeacherProfile(userId: string): Promise<TeacherProfile | null> {
    try {
      const { data, error } = await supabase
        .from('teachers')
        .select(`
          *,
          user:users(*),
          classes(*),
          students(
            *,
            user:users(*)
          )
        `)
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      return data as TeacherProfile;
    } catch (error) {
      console.error('Error fetching teacher profile:', error);
      return null;
    }
  }

  async createClass(teacherId: string, classData: Omit<Inserts<'classes'>, 'teacher_id'>) {
    try {
      const { data, error } = await supabase
        .from('classes')
        .insert({
          ...classData,
          teacher_id: teacherId,
        })
        .select()
        .single();

      if (error) throw error;
      
      // Add to sync queue for multi-device
      await multiDeviceService.addPendingAction('create_class', data);
      
      return data;
    } catch (error) {
      console.error('Error creating class:', error);
      throw error;
    }
  }

  async enrollStudentInClass(classId: string, studentId: string) {
    try {
      const { data, error } = await supabase
        .from('class_enrollments')
        .insert({
          class_id: classId,
          student_id: studentId,
        })
        .select()
        .single();

      if (error) throw error;
      
      await multiDeviceService.addPendingAction('enroll_student', data);
      
      return data;
    } catch (error) {
      console.error('Error enrolling student in class:', error);
      throw error;
    }
  }

  async getTeacherClasses(teacherId: string): Promise<ClassWithStudents[]> {
    try {
      const { data, error } = await supabase
        .from('classes')
        .select(`
          *,
          enrollments:class_enrollments(
            *,
            student:students(
              *,
              user:users(*)
            )
          )
        `)
        .eq('teacher_id', teacherId)
        .eq('is_active', true);

      if (error) throw error;

      // Transform data to include students directly
      const classesWithStudents = data?.map(cls => ({
        ...cls,
        students: cls.enrollments?.map(enrollment => enrollment.student) || [],
      })) || [];

      return classesWithStudents as ClassWithStudents[];
    } catch (error) {
      console.error('Error fetching teacher classes:', error);
      return [];
    }
  }

  // ============= LESSON MANAGEMENT =============

  async createLesson(teacherId: string, lessonData: Omit<Inserts<'lessons'>, 'teacher_id'>) {
    try {
      const { data, error } = await supabase
        .from('lessons')
        .insert({
          ...lessonData,
          teacher_id: teacherId,
        })
        .select()
        .single();

      if (error) throw error;
      
      await multiDeviceService.addPendingAction('create_lesson', data);
      
      return data;
    } catch (error) {
      console.error('Error creating lesson:', error);
      throw error;
    }
  }

  async updateLesson(lessonId: string, updates: Updates<'lessons'>) {
    try {
      const { data, error } = await supabase
        .from('lessons')
        .update(updates)
        .eq('id', lessonId)
        .select()
        .single();

      if (error) throw error;
      
      await multiDeviceService.addPendingAction('update_lesson', data);
      
      return data;
    } catch (error) {
      console.error('Error updating lesson:', error);
      throw error;
    }
  }

  async getTeacherLessons(teacherId: string): Promise<LessonWithExercises[]> {
    try {
      const { data, error } = await supabase
        .from('lessons')
        .select(`
          *,
          exercises(*),
          class:classes(*)
        `)
        .eq('teacher_id', teacherId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as LessonWithExercises[];
    } catch (error) {
      console.error('Error fetching teacher lessons:', error);
      return [];
    }
  }

  async getStudentLessons(studentId: string): Promise<LessonWithExercises[]> {
    try {
      const { data, error } = await supabase
        .from('lessons')
        .select(`
          *,
          exercises(*),
          class:classes(*)
        `)
        .eq('is_published', true)
        .in('class_id', 
          supabase
            .from('class_enrollments')
            .select('class_id')
            .eq('student_id', studentId)
        )
        .order('order_index', { ascending: true });

      if (error) throw error;
      return data as LessonWithExercises[];
    } catch (error) {
      console.error('Error fetching student lessons:', error);
      return [];
    }
  }

  // ============= EXERCISE MANAGEMENT =============

  async createExercise(teacherId: string, exerciseData: Omit<Inserts<'exercises'>, 'teacher_id'>) {
    try {
      const { data, error } = await supabase
        .from('exercises')
        .insert({
          ...exerciseData,
          teacher_id: teacherId,
        })
        .select()
        .single();

      if (error) throw error;
      
      await multiDeviceService.addPendingAction('create_exercise', data);
      
      return data;
    } catch (error) {
      console.error('Error creating exercise:', error);
      throw error;
    }
  }

  async getExercisesByLesson(lessonId: string) {
    try {
      const { data, error } = await supabase
        .from('exercises')
        .select('*')
        .eq('lesson_id', lessonId)
        .eq('is_published', true)
        .order('order_index', { ascending: true });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching exercises by lesson:', error);
      return [];
    }
  }

  // ============= STUDENT PROGRESS =============

  async updateStudentProgress(
    studentId: string, 
    progressData: {
      lesson_id?: string;
      exercise_id?: string;
      status: 'not_started' | 'in_progress' | 'completed';
      score?: number;
      time_spent?: number;
      answers?: any;
    }
  ) {
    try {
      const { data, error } = await supabase
        .from('student_progress')
        .upsert({
          student_id: studentId,
          ...progressData,
          last_attempt_at: new Date().toISOString(),
          ...(progressData.status === 'completed' && {
            completed_at: new Date().toISOString()
          }),
          ...(progressData.status === 'in_progress' && !progressData.exercise_id && {
            started_at: new Date().toISOString()
          }),
        })
        .select()
        .single();

      if (error) throw error;
      
      // Update learning streak if completed
      if (progressData.status === 'completed') {
        await this.updateLearningStreak(studentId);
      }
      
      await multiDeviceService.addPendingAction('update_progress', data);
      
      return data;
    } catch (error) {
      console.error('Error updating student progress:', error);
      throw error;
    }
  }

  async getStudentProgress(studentId: string, lessonId?: string, exerciseId?: string) {
    try {
      let query = supabase
        .from('student_progress')
        .select(`
          *,
          lesson:lessons(*),
          exercise:exercises(*)
        `)
        .eq('student_id', studentId);

      if (lessonId) query = query.eq('lesson_id', lessonId);
      if (exerciseId) query = query.eq('exercise_id', exerciseId);

      const { data, error } = await query;

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching student progress:', error);
      return [];
    }
  }

  async getStudentAnalytics(studentId: string) {
    try {
      const [progressData, achievementsData, streakData] = await Promise.all([
        supabase
          .from('student_progress')
          .select(`
            *,
            lesson:lessons(subject, difficulty),
            exercise:exercises(subject, difficulty, points)
          `)
          .eq('student_id', studentId),
        
        supabase
          .from('achievements')
          .select('*')
          .eq('student_id', studentId)
          .order('earned_at', { ascending: false }),
        
        supabase
          .from('learning_streaks')
          .select('*')
          .eq('student_id', studentId)
          .single()
      ]);

      if (progressData.error) throw progressData.error;

      return {
        progress: progressData.data || [],
        achievements: achievementsData.data || [],
        streak: streakData.data || null,
      };
    } catch (error) {
      console.error('Error fetching student analytics:', error);
      return {
        progress: [],
        achievements: [],
        streak: null,
      };
    }
  }

  // ============= ACHIEVEMENTS & GAMIFICATION =============

  async awardAchievement(studentId: string, achievementData: Omit<Inserts<'achievements'>, 'student_id'>) {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .insert({
          ...achievementData,
          student_id: studentId,
        })
        .select()
        .single();

      if (error) throw error;
      
      await multiDeviceService.addPendingAction('award_achievement', data);
      
      return data;
    } catch (error) {
      console.error('Error awarding achievement:', error);
      throw error;
    }
  }

  async updateLearningStreak(studentId: string) {
    try {
      const today = new Date().toDateString();
      
      const { data: currentStreak, error: streakError } = await supabase
        .from('learning_streaks')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (streakError && streakError.code !== 'PGRST116') {
        throw streakError;
      }

      const yesterday = new Date(Date.now() - 86400000).toDateString();
      let newStreakData;

      if (!currentStreak) {
        // Create new streak record
        newStreakData = {
          student_id: studentId,
          current_streak: 1,
          longest_streak: 1,
          last_activity_date: today,
          streak_start_date: today,
          total_learning_days: 1,
        };
      } else {
        const lastActivity = currentStreak.last_activity_date;
        
        if (lastActivity === today) {
          // Already updated today
          return currentStreak;
        } else if (lastActivity === yesterday) {
          // Continue streak
          const newStreak = currentStreak.current_streak + 1;
          newStreakData = {
            current_streak: newStreak,
            longest_streak: Math.max(newStreak, currentStreak.longest_streak),
            last_activity_date: today,
            total_learning_days: currentStreak.total_learning_days + 1,
          };
        } else {
          // Streak broken, start new
          newStreakData = {
            current_streak: 1,
            longest_streak: currentStreak.longest_streak,
            last_activity_date: today,
            streak_start_date: today,
            total_learning_days: currentStreak.total_learning_days + 1,
          };
        }
      }

      const { data, error } = await supabase
        .from('learning_streaks')
        .upsert(newStreakData, { onConflict: 'student_id' })
        .select()
        .single();

      if (error) throw error;

      // Award streak achievements
      if (newStreakData.current_streak && newStreakData.current_streak % 7 === 0) {
        await this.awardAchievement(studentId, {
          type: 'streak',
          title: `${newStreakData.current_streak} Day Streak!`,
          description: `Maintained a learning streak for ${newStreakData.current_streak} days`,
          icon: '🔥',
          points: newStreakData.current_streak * 5,
        });
      }

      return data;
    } catch (error) {
      console.error('Error updating learning streak:', error);
      throw error;
    }
  }

  // ============= CLASS MANAGEMENT =============

  async getClassStudents(classId: string): Promise<StudentWithProgress[]> {
    try {
      const { data, error } = await supabase
        .from('class_enrollments')
        .select(`
          student:students(
            *,
            user:users(*),
            progress:student_progress(*),
            achievements:achievements(*),
            learning_streak:learning_streaks(*)
          )
        `)
        .eq('class_id', classId)
        .eq('is_active', true);

      if (error) throw error;

      return data?.map(enrollment => enrollment.student) as StudentWithProgress[] || [];
    } catch (error) {
      console.error('Error fetching class students:', error);
      return [];
    }
  }

  // ============= NOTIFICATIONS =============

  async createNotification(userId: string, notification: Omit<Inserts<'notifications'>, 'user_id'>) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert({
          ...notification,
          user_id: userId,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  async getUserNotifications(userId: string, limit = 20) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  async markNotificationAsRead(notificationId: string) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }
}

export const apiService = new ApiService();
export default ApiService;
