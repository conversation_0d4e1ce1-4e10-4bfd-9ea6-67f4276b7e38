import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, Button, ScrollView, TouchableOpacity, Alert, Modal, Pressable } from 'react-native';
import { saveExercises, loadExercises, Exercise, MultipleChoiceQuestion } from '../services/StorageService';
import 'react-native-get-random-values'; // Import for UUID generation
import { v4 as uuidv4 } from 'uuid';

type Question = MultipleChoiceQuestion;

import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';

type ExerciseBuilderScreenProps = NativeStackScreenProps<RootStackParamList, 'ExerciseBuilder'>;

const ExerciseBuilderScreen = ({ navigation, route }: ExerciseBuilderScreenProps) => {
  const [exerciseId, setExerciseId] = useState(uuidv4()); // Unique ID for the exercise
  const [exerciseTitle, setExerciseTitle] = useState('');
  const [exerciseDescription, setExerciseDescription] = useState('');
  const [questions, setQuestions] = useState<Question[]>([]); // This state holds an array of individual questions
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newQuestionText, setNewQuestionText] = useState('');
  const [newOptions, setNewOptions] = useState<string[]>(['', '', '', '']);
  const [correctOptionIndex, setCorrectOptionIndex] = useState<number>(0);


  useEffect(() => {
    const fetchAndSetExercise = async () => {
      const { exerciseId: paramExerciseId } = route.params || {};
      if (paramExerciseId) {
        const storedExercises = await loadExercises();
        const exerciseToEdit = storedExercises.find(ex => ex.id === paramExerciseId);
        if (exerciseToEdit) {
          setExerciseId(exerciseToEdit.id);
          setExerciseTitle(exerciseToEdit.exerciseTitle);
          setExerciseDescription(exerciseToEdit.exerciseDescription);
          setQuestions(exerciseToEdit.questions);
        } else {
          // If exerciseId is provided but not found, create a new one
          setExerciseId(uuidv4());
          setExerciseTitle('');
          setExerciseDescription('');
          setQuestions([]);
        }
      } else {
        // If no exerciseId is provided, initialize a new exercise
        setExerciseId(uuidv4());
        setExerciseTitle('');
        setExerciseDescription('');
        setQuestions([]);
      }
    };
    fetchAndSetExercise();
  }, [route.params]); // Re-run effect when route params change


  const handleAddQuestion = () => {
    setIsModalVisible(true);
    setNewQuestionText('');
    setNewOptions(['', '', '', '']);
    setCorrectOptionIndex(0);
  };

  const handleSaveNewQuestion = () => {
    if (newQuestionText.trim() === '') {
      Alert.alert('Error', 'Question text cannot be empty.');
      return;
    }

    const validOptions = newOptions.filter(opt => opt.trim() !== '');
    if (validOptions.length < 2) {
      Alert.alert('Error', 'Please provide at least two options.');
      return;
    }
    if (correctOptionIndex >= validOptions.length) {
      Alert.alert('Error', 'Correct option is out of bounds.');
      return;
    }

    const optionsWithIds = validOptions.map((text) => ({
      id: uuidv4(), // Use uuid for option ID
      text: text.trim(),
    }));

    const newMCQuestion: MultipleChoiceQuestion = {
      id: uuidv4(), // Use uuid for question ID
      type: 'multiple-choice',
      questionText: newQuestionText.trim(),
      options: optionsWithIds,
      correctAnswerId: optionsWithIds[correctOptionIndex].id,
    };

    setQuestions([...questions, newMCQuestion]);
    setIsModalVisible(false);
  };

  const handleSaveExercise = async () => {
    const exerciseToSave: Exercise = {
      id: exerciseId,
      exerciseTitle,
      exerciseDescription,
      questions: questions as MultipleChoiceQuestion[], // Cast to MultipleChoiceQuestion[]
    };
    const currentExercises = await loadExercises();
    const existingIndex = currentExercises.findIndex(ex => ex.id === exerciseId);

    let updatedExercises: Exercise[];
    if (existingIndex > -1) {
      updatedExercises = currentExercises.map((ex, index) =>
        index === existingIndex ? exerciseToSave : ex
      );
    } else {
      updatedExercises = [...currentExercises, exerciseToSave];
    }

    await saveExercises(updatedExercises);
    Alert.alert('Exercise Saved!', 'Your exercise has been saved locally.');
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Exercise Builder</Text>

      <Text style={styles.label}>Exercise Title:</Text>
      <TextInput
        style={styles.input}
        placeholder="e.g., Alphabet Matching"
        value={exerciseTitle}
        onChangeText={setExerciseTitle}
      />

      <Text style={styles.label}>Exercise Description:</Text>
      <TextInput
        style={[styles.input, styles.textArea]}
        placeholder="Briefly describe the exercise objectives."
        value={exerciseDescription}
        onChangeText={setExerciseDescription}
        multiline
      />

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Questions:</Text>
        {questions.map((q, index) => (
          <View key={q.id} style={styles.questionCard}>
            <Text style={styles.questionCardText}>Question {index + 1} ({q.type}):</Text>
            {q.type === 'multiple-choice' && (
              <View>
                <Text>{q.questionText}</Text>
                {q.options.map((option) => (
                  <Text key={option.id} style={option.id === q.correctAnswerId ? styles.correctOption : {}}>
                    - {option.text} {option.id === q.correctAnswerId ? '(Correct)' : ''}
                  </Text>
                ))}
              </View>
            )}
          </View>
        ))}
        <Button title="Add New Question" onPress={handleAddQuestion} />
      </View>

      <View style={styles.saveButtonContainer}>
        <Button title="Save Exercise" onPress={handleSaveExercise} color="#007bff" />
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={isModalVisible}
        onRequestClose={() => {
          Alert.alert('Modal has been closed.');
          setIsModalVisible(!isModalVisible);
        }}>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text style={styles.modalTitle}>Add New Question</Text>
            {/* Simplified to only multiple choice for now */}
            <View>
              <Text style={styles.modalLabel}>Question Text:</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="Enter question text"
                value={newQuestionText}
                onChangeText={setNewQuestionText}
                multiline
              />
              <Text style={styles.modalLabel}>Options:</Text>
              {newOptions.map((option, index) => (
                <TextInput
                  key={index}
                  style={styles.modalInput}
                  placeholder={`Option ${index + 1}`}
                  value={option}
                  onChangeText={(text) => {
                    const updatedOptions = [...newOptions];
                    updatedOptions[index] = text;
                    setNewOptions(updatedOptions);
                  }}
                />
              ))}
              <Text style={styles.modalLabel}>Correct Option (Index):</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="e.g., 0 for first option"
                keyboardType="numeric"
                value={correctOptionIndex.toString()}
                onChangeText={(text) => setCorrectOptionIndex(Number(text))}
              />
            </View>

            <Pressable
              style={[styles.button, styles.buttonClose]}
              onPress={handleSaveNewQuestion}>
              <Text style={styles.textStyle}>Save Question</Text>
            </Pressable>
            <Pressable
              style={[styles.button, styles.buttonClose]}
              onPress={() => setIsModalVisible(!isModalVisible)}>
              <Text style={styles.textStyle}>Cancel</Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8f8f8',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 25,
    color: '#333',
    textAlign: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#555',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    backgroundColor: '#fff',
    fontSize: 16,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  section: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  questionCard: {
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginBottom: 10,
    backgroundColor: '#f9f9f9',
  },
  saveButtonContainer: {
    marginTop: 30,
    marginBottom: 50,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 10, // Ensure modal is on top
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
    alignSelf: 'flex-start',
    marginTop: 10,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    width: '100%',
    marginBottom: 10,
    backgroundColor: '#fff',
  },
  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2,
    marginTop: 10,
    minWidth: 120,
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  questionCardText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  correctOption: {
    fontWeight: 'bold',
    color: 'green',
  },
});

export default ExerciseBuilderScreen;
