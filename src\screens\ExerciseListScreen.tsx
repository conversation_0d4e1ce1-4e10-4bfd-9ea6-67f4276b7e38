import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, FlatList, TouchableOpacity, Alert } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { loadExercises, Exercise } from '../services/StorageService';

type ExerciseListScreenProps = NativeStackScreenProps<RootStackParamList, 'ExerciseList'>;

const ExerciseListScreen = ({ navigation }: ExerciseListScreenProps) => {
  const [exercises, setExercises] = useState<Exercise[]>([]);

  const fetchExercises = async () => {
    const storedExercises = await loadExercises();
    setExercises(storedExercises);
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchExercises();
    });
    return unsubscribe;
  }, [navigation]);

  const handleCreateNewExercise = () => {
    navigation.navigate('ExerciseBuilder', {}); // Navigate without an exerciseId to create a new one
  };

  const handleEditExercise = (exerciseId: string) => {
    navigation.navigate('ExerciseBuilder', { exerciseId: exerciseId }); // Pass exerciseId to edit existing exercise
  };

  const handleDeleteExercise = (exerciseId: string) => {
    Alert.alert(
      "Delete Exercise",
      "Are you sure you want to delete this exercise?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          onPress: async () => {
            // TODO: Implement actual delete logic in StorageService
            Alert.alert("Delete", "Exercise deletion is not yet persistent. Feature coming soon.");
            fetchExercises(); // Re-fetch to show current state
          },
          style: "destructive"
        }
      ]
    );
  };

  const renderItem = ({ item }: { item: Exercise }) => (
    <View style={styles.exerciseItem}>
      <Text style={styles.exerciseTitle}>{item.exerciseTitle || 'Untitled Exercise'}</Text>
      <Text style={styles.exerciseDescription}>{item.exerciseDescription || 'No description'}</Text>
      <View style={styles.exerciseActions}>
        <Button title="Edit" onPress={() => handleEditExercise(item.id)} />
        <View style={{ width: 10 }} />
        <Button title="Delete" onPress={() => handleDeleteExercise(item.id)} color="red" />
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>My Exercises</Text>
      <Button title="Create New Exercise" onPress={handleCreateNewExercise} />
      <View style={{ height: 20 }} />
      {exercises.length === 0 ? (
        <Text>No exercises created yet. Start by creating a new one!</Text>
      ) : (
        <FlatList
          data={exercises}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          style={styles.exerciseList}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8f8f8',
  },
  header: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  exerciseList: {
    marginTop: 10,
    width: '100%',
  },
  exerciseItem: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eee',
  },
  exerciseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  exerciseDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
    marginBottom: 10,
  },
  exerciseActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
});

export default ExerciseListScreen;
