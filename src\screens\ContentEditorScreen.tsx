import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, Button, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { saveLessons, loadLessons, Lesson } from '../services/StorageService';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';

type ContentEditorScreenProps = NativeStackScreenProps<RootStackParamList, 'ContentEditor'>;

const ContentEditorScreen = ({ navigation, route }: ContentEditorScreenProps) => {
  const [lessonId, setLessonId] = useState(uuidv4()); // Unique ID for the lesson
  const [lessonTitle, setLessonTitle] = useState('');
  const [lessonDescription, setLessonDescription] = useState('');
  const [contentSections, setContentSections] = useState<{ id: string; type: 'text' | 'image' | 'video' | 'audio'; value: string }[]>([]);
  const [nextContentId, setNextContentId] = useState(0); // This can be removed if using UUIDs for content sections

  useEffect(() => {
    const fetchAndSetLesson = async () => {
      const { lessonId: paramLessonId } = route.params || {};
      if (paramLessonId) {
        const storedLessons = await loadLessons();
        const lessonToEdit = storedLessons.find(lesson => lesson.id === paramLessonId);
        if (lessonToEdit) {
          setLessonId(lessonToEdit.id);
          setLessonTitle(lessonToEdit.title);
          setLessonDescription(lessonToEdit.description);
          setContentSections(lessonToEdit.contentSections);
        } else {
          // If lessonId is provided but not found, create a new one
          setLessonId(uuidv4());
          setLessonTitle('');
          setLessonDescription('');
          setContentSections([]);
        }
      } else {
        // If no lessonId is provided, initialize a new lesson
        setLessonId(uuidv4());
        setLessonTitle('');
        setLessonDescription('');
        setContentSections([]);
      }
    };
    fetchAndSetLesson();
  }, [route.params]); // Re-run effect when route params change

  const addContentSection = (type: 'text' | 'image' | 'video' | 'audio') => {
    setContentSections([...contentSections, { id: uuidv4(), type, value: '' }]); // Use uuid for content section ID
  };

  const updateContentSection = (id: string, value: string) => {
    setContentSections(contentSections.map(section => section.id === id ? { ...section, value } : section));
  };

  const handleSaveLesson = async () => {
    const lessonToSave: Lesson = {
      id: lessonId,
      title: lessonTitle,
      description: lessonDescription,
      contentSections,
      exerciseIds: [], // TODO: Link exercises to lessons later
    };
    const currentLessons = await loadLessons();
    const existingIndex = currentLessons.findIndex(lesson => lesson.id === lessonId);

    let updatedLessons: Lesson[];
    if (existingIndex > -1) {
      updatedLessons = currentLessons.map((lesson, index) =>
        index === existingIndex ? lessonToSave : lesson
      );
    } else {
      updatedLessons = [...currentLessons, lessonToSave];
    }

    await saveLessons(updatedLessons);
    Alert.alert('Lesson Saved!', 'Your lesson has been saved locally.');
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Create New Lesson</Text>

      <Text style={styles.label}>Lesson Title:</Text>
      <TextInput
        style={styles.input}
        placeholder="e.g., Introduction to Alphabets"
        value={lessonTitle}
        onChangeText={setLessonTitle}
      />

      <Text style={styles.label}>Lesson Description:</Text>
      <TextInput
        style={[styles.input, styles.textArea]}
        placeholder="Briefly describe the lesson objectives."
        value={lessonDescription}
        onChangeText={setLessonDescription}
        multiline
      />

      <Text style={styles.sectionTitle}>Content Sections:</Text>
      {contentSections.map(section => (
        <View key={section.id} style={styles.contentSection}>
          <Text style={styles.sectionLabel}>{section.type.toUpperCase()} Content:</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder={`Enter ${section.type} content (e.g., text, image URL, video URL, audio URL)`}
            value={section.value}
            onChangeText={(text) => updateContentSection(section.id, text)}
            multiline
          />
        </View>
      ))}

      <View style={styles.buttonGroup}>
        <TouchableOpacity style={styles.addButton} onPress={() => addContentSection('text')}>
          <Text style={styles.addButtonText}>Add Text</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.addButton} onPress={() => addContentSection('image')}>
          <Text style={styles.addButtonText}>Add Image</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.addButton} onPress={() => addContentSection('video')}>
          <Text style={styles.addButtonText}>Add Video</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.addButton} onPress={() => addContentSection('audio')}>
          <Text style={styles.addButtonText}>Add Audio</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.separator} />

      <View style={styles.saveButtonContainer}>
        <Button title="Save Lesson" onPress={handleSaveLesson} color="#007bff" />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8f8f8',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 25,
    color: '#333',
    textAlign: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#555',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    backgroundColor: '#fff',
    fontSize: 16,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 15,
    color: '#333',
  },
  contentSection: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    marginBottom: 15,
  },
  sectionLabel: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 5,
    color: '#666',
  },
  buttonGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  addButton: {
    backgroundColor: '#28a745',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    margin: 5,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 20,
  },
  saveButtonContainer: {
    marginTop: 30,
    marginBottom: 50,
  },
});

export default ContentEditorScreen;
