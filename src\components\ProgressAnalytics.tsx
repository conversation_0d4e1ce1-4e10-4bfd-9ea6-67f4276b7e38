import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/theme';

interface ProgressData {
  subject: string;
  completed: number;
  total: number;
  averageScore: number;
  timeSpent: number; // in minutes
  lastActivity: Date;
  streak: number;
}

interface ProgressAnalyticsProps {
  data: ProgressData[];
  studentName: string;
  variant: 'teacher' | 'student';
}

const ProgressAnalytics: React.FC<ProgressAnalyticsProps> = ({ 
  data, 
  studentName, 
  variant 
}) => {
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return Colors.success[500];
    if (percentage >= 60) return Colors.warning[500];
    return Colors.error[500];
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const renderProgressBar = (percentage: number) => (
    <View style={styles.progressBarContainer}>
      <View style={[styles.progressBar, { backgroundColor: Colors.neutral[200] }]}>
        <View
          style={[
            styles.progressFill,
            { 
              width: `${percentage}%`, 
              backgroundColor: getProgressColor(percentage) 
            }
          ]}
        />
      </View>
      <Text style={styles.progressPercentage}>{percentage}%</Text>
    </View>
  );

  const renderSubjectCard = (subject: ProgressData) => {
    const completionRate = Math.round((subject.completed / subject.total) * 100);
    
    return (
      <View key={subject.subject} style={styles.subjectCard}>
        <View style={styles.subjectHeader}>
          <Text style={styles.subjectName}>{subject.subject}</Text>
          <View style={[styles.streakBadge, { backgroundColor: Colors.student.success }]}>
            <Text style={styles.streakText}>🔥 {subject.streak}</Text>
          </View>
        </View>
        
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Completed</Text>
            <Text style={styles.statValue}>{subject.completed}/{subject.total}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Avg Score</Text>
            <Text style={styles.statValue}>{subject.averageScore}%</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Time</Text>
            <Text style={styles.statValue}>{formatTime(subject.timeSpent)}</Text>
          </View>
        </View>
        
        {renderProgressBar(completionRate)}
        
        <Text style={styles.lastActivity}>
          Last activity: {subject.lastActivity.toLocaleDateString()}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {variant === 'teacher' ? `${studentName}'s Progress` : 'My Learning Journey'}
        </Text>
        <Text style={styles.subtitle}>
          {variant === 'student' ? 'Keep up the great work! 🌟' : 'Detailed performance analysis'}
        </Text>
      </View>
      
      {data.map(renderSubjectCard)}
      
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Overall Summary</Text>
        <View style={styles.summaryStats}>
          <View style={styles.summaryStatItem}>
            <Text style={styles.summaryStatValue}>
              {Math.round(data.reduce((acc, s) => acc + (s.completed/s.total), 0) / data.length * 100)}%
            </Text>
            <Text style={styles.summaryStatLabel}>Overall Progress</Text>
          </View>
          <View style={styles.summaryStatItem}>
            <Text style={styles.summaryStatValue}>
              {formatTime(data.reduce((acc, s) => acc + s.timeSpent, 0))}
            </Text>
            <Text style={styles.summaryStatLabel}>Total Time</Text>
          </View>
          <View style={styles.summaryStatItem}>
            <Text style={styles.summaryStatValue}>
              {Math.max(...data.map(s => s.streak))}
            </Text>
            <Text style={styles.summaryStatLabel}>Best Streak</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  subjectCard: {
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    elevation: 2,
    shadowColor: Colors.neutral[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  subjectName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '600' as const,
    color: Colors.text.primary,
  },
  streakBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.full,
  },
  streakText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.inverse,
    fontWeight: '600' as const,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs / 2,
  },
  statValue: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600' as const,
    color: Colors.text.primary,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: Spacing.sm,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    minWidth: 35,
  },
  lastActivity: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.tertiary,
    fontStyle: 'italic',
  },
  summaryCard: {
    backgroundColor: Colors.student.secondary + '20',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginTop: Spacing.md,
  },
  summaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryStatItem: {
    alignItems: 'center',
  },
  summaryStatValue: {
    fontSize: Typography.fontSize.xl,
    fontWeight: '700' as const,
    color: Colors.student.primary,
    marginBottom: Spacing.xs / 2,
  },
  summaryStatLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
});

export default ProgressAnalytics;
