import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LoginScreen from '../screens/LoginScreen';
import WelcomeScreen from '../screens/WelcomeScreen';
import TeacherDashboardScreen from '../screens/TeacherDashboardScreen';
import StudentHomeScreen from '../screens/StudentHomeScreen';
import KGAlphabetExerciseScreen from '../screens/KGAlphabetExerciseScreen';
import ContentEditorScreen from '../screens/ContentEditorScreen';
import ExerciseBuilderScreen from '../screens/ExerciseBuilderScreen';
import LessonListScreen from '../screens/LessonListScreen';
import ExerciseListScreen from '../screens/ExerciseListScreen';
import StudentListScreen from '../screens/StudentListScreen';
import StudentProfileScreen from '../screens/StudentProfileScreen';

export type RootStackParamList = {
  Login: undefined;
  Welcome: undefined; // This will be replaced or removed later
  TeacherDashboard: undefined;
  StudentHome: undefined;
  KGAlphabetExercise: undefined;
  ContentEditor: { lessonId?: string }; // ContentEditor can now receive an optional lessonId
  ExerciseBuilder: { exerciseId?: string }; // ExerciseBuilder can now receive an optional exerciseId
  LessonList: undefined; // New screen for listing lessons
  ExerciseList: undefined; // New screen for listing exercises
  StudentList: undefined; // New screen for listing students
  StudentProfile: { mode: 'view' | 'edit' | 'create'; studentId?: string }; // Student profile screen
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Login">
      <Stack.Screen name="Login" component={LoginScreen} options={{ headerShown: false }} />
      <Stack.Screen name="TeacherDashboard" component={TeacherDashboardScreen} options={{ title: 'Teacher Dashboard' }} />
      <Stack.Screen name="StudentHome" component={StudentHomeScreen} options={{ title: 'My Learning Path' }} />
      <Stack.Screen name="KGAlphabetExercise" component={KGAlphabetExerciseScreen} options={{ title: 'Alphabet Exercise' }} />
      <Stack.Screen name="ContentEditor" component={ContentEditorScreen} options={{ title: 'Content Editor' }} />
      <Stack.Screen name="ExerciseBuilder" component={ExerciseBuilderScreen} options={{ title: 'Exercise Builder' }} />
      <Stack.Screen name="LessonList" component={LessonListScreen} options={{ title: 'My Lessons' }} />
      <Stack.Screen name="ExerciseList" component={ExerciseListScreen} options={{ title: 'My Exercises' }} />
      <Stack.Screen name="StudentList" component={StudentListScreen} options={{ title: 'Student Roster' }} />
      <Stack.Screen name="StudentProfile" component={StudentProfileScreen} options={{ title: 'Student Profile' }} />
      {/* WelcomeScreen is now optional or can be removed, depending on the direct navigation */}
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
