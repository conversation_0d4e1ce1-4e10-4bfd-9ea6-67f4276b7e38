import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { supabase } from '../../services/supabase';

export interface Achievement {
  id: string;
  type: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  earned_at?: string;
  progress?: number;
  maxProgress?: number;
  unlocked: boolean;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  earned: boolean;
  earnedDate?: string;
}

export interface Streak {
  current: number;
  longest: number;
  lastActivityDate?: string;
}

interface GamificationState {
  totalPoints: number;
  level: number;
  pointsToNextLevel: number;
  achievements: Achievement[];
  badges: Badge[];
  streak: Streak;
  weeklyGoal: {
    target: number;
    current: number;
    week: string;
  };
  loading: boolean;
  error: string | null;
}

const initialState: GamificationState = {
  totalPoints: 0,
  level: 1,
  pointsToNextLevel: 100,
  achievements: [],
  badges: [],
  streak: {
    current: 0,
    longest: 0,
  },
  weeklyGoal: {
    target: 300,
    current: 0,
    week: new Date().toISOString().slice(0, 10),
  },
  loading: false,
  error: null,
};

// Calculate level from total points
const calculateLevel = (points: number): { level: number; pointsToNext: number } => {
  const basePoints = 100;
  const multiplier = 1.5;
  
  let level = 1;
  let totalNeeded = 0;
  
  while (totalNeeded <= points) {
    const pointsForLevel = Math.floor(basePoints * Math.pow(multiplier, level - 1));
    totalNeeded += pointsForLevel;
    if (totalNeeded <= points) {
      level++;
    }
  }
  
  const currentLevelStart = totalNeeded - Math.floor(basePoints * Math.pow(multiplier, level - 1));
  const pointsToNext = totalNeeded - points;
  
  return { level: level - 1, pointsToNext };
};

// Async thunks
export const fetchUserGamificationData = createAsyncThunk(
  'gamification/fetchUserData',
  async (userId: string, { rejectWithValue }) => {
    try {
      const [achievementsResponse, pointsResponse] = await Promise.all([
        supabase
          .from('achievements')
          .select('*')
          .eq('student_id', userId)
          .order('earned_at', { ascending: false }),
        supabase
          .from('student_progress')
          .select('score, time_spent, completed_at')
          .eq('student_id', userId)
          .eq('status', 'completed')
      ]);

      if (achievementsResponse.error) throw achievementsResponse.error;
      if (pointsResponse.error) throw pointsResponse.error;

      // Calculate total points from completed exercises
      const totalPoints = pointsResponse.data?.reduce((sum, progress) => {
        return sum + (progress.score || 0);
      }, 0) || 0;

      return {
        achievements: achievementsResponse.data || [],
        totalPoints,
        completedExercises: pointsResponse.data || [],
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const awardAchievement = createAsyncThunk(
  'gamification/awardAchievement',
  async ({
    studentId,
    type,
    title,
    description,
    icon,
    points
  }: {
    studentId: string;
    type: string;
    title: string;
    description: string;
    icon: string;
    points: number;
  }, { rejectWithValue }) => {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .insert({
          student_id: studentId,
          type,
          title,
          description,
          icon,
          points,
          earned_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateStreak = createAsyncThunk(
  'gamification/updateStreak',
  async (userId: string, { getState, rejectWithValue }) => {
    try {
      const today = new Date().toDateString();
      const state = getState() as any;
      const lastActivity = state.gamification.streak.lastActivityDate;
      const yesterday = new Date(Date.now() - 86400000).toDateString();
      
      let newStreak = 1;
      
      if (lastActivity === yesterday) {
        newStreak = state.gamification.streak.current + 1;
      } else if (lastActivity === today) {
        // Already updated today
        return state.gamification.streak;
      }

      return {
        current: newStreak,
        longest: Math.max(newStreak, state.gamification.streak.longest),
        lastActivityDate: today,
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const gamificationSlice = createSlice({
  name: 'gamification',
  initialState,
  reducers: {
    addPoints: (state, action: PayloadAction<number>) => {
      state.totalPoints += action.payload;
      const { level, pointsToNext } = calculateLevel(state.totalPoints);
      
      if (level > state.level) {
        // Level up achieved!
        state.level = level;
        // Could trigger level up achievement here
      }
      
      state.level = level;
      state.pointsToNextLevel = pointsToNext;
    },
    
    updateWeeklyGoal: (state, action: PayloadAction<number>) => {
      const currentWeek = new Date().toISOString().slice(0, 10);
      
      if (state.weeklyGoal.week !== currentWeek) {
        // New week, reset progress
        state.weeklyGoal = {
          target: state.weeklyGoal.target,
          current: action.payload,
          week: currentWeek,
        };
      } else {
        state.weeklyGoal.current += action.payload;
      }
    },
    
    unlockBadge: (state, action: PayloadAction<Badge>) => {
      const existingBadge = state.badges.find(b => b.id === action.payload.id);
      if (!existingBadge) {
        state.badges.push({
          ...action.payload,
          earned: true,
          earnedDate: new Date().toISOString(),
        });
      }
    },
    
    resetGamification: (state) => {
      return { ...initialState };
    },
  },
  
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserGamificationData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserGamificationData.fulfilled, (state, action) => {
        state.loading = false;
        state.achievements = action.payload.achievements;
        state.totalPoints = action.payload.totalPoints;
        
        const { level, pointsToNext } = calculateLevel(state.totalPoints);
        state.level = level;
        state.pointsToNextLevel = pointsToNext;
      })
      .addCase(fetchUserGamificationData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      .addCase(awardAchievement.fulfilled, (state, action) => {
        state.achievements.unshift(action.payload);
        state.totalPoints += action.payload.points;
        
        const { level, pointsToNext } = calculateLevel(state.totalPoints);
        state.level = level;
        state.pointsToNextLevel = pointsToNext;
      })
      
      .addCase(updateStreak.fulfilled, (state, action) => {
        state.streak = action.payload;
      });
  },
});

export const { 
  addPoints, 
  updateWeeklyGoal, 
  unlockBadge, 
  resetGamification 
} = gamificationSlice.actions;

export default gamificationSlice.reducer;
