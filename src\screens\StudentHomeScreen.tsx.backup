import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';

type StudentHomeScreenProps = NativeStackScreenProps<RootStackParamList, 'StudentHome'>;

const StudentHomeScreen = ({ navigation }: StudentHomeScreenProps) => {
  // Placeholder for assigned lessons/exercises
  const assignedContent = [
    { id: '1', title: 'Alphabet Recognition (KG)', status: 'In Progress' },
    { id: '2', title: 'Numbers 1-10 (KG)', status: 'Completed' },
    { id: '3', title: 'Basic Addition (Grade 1)', status: 'Not Started' },
  ];

  return (
    <ScrollView style={styles.scrollViewContainer}>
      <View style={styles.container}>
        <Text style={styles.title}>My Learning Path</Text>
        <Text style={styles.welcomeText}>Hello, Student!</Text>

        {assignedContent.length > 0 ? (
          assignedContent.map((item) => (
            <View key={item.id} style={styles.contentCard}>
              <Text style={styles.contentTitle}>{item.title}</Text>
              <Text style={styles.contentStatus}>Status: {item.status}</Text>
              {/* Add a button to start/continue lesson here */}
              {item.id === '1' && ( // Only show button for Alphabet Recognition for now
                <TouchableOpacity
                  style={styles.startButton}
                  onPress={() => navigation.navigate('KGAlphabetExercise')}
                >
                  <Text style={styles.startButtonText}>Start Lesson</Text>
                </TouchableOpacity>
              )}
            </View>
          ))
        ) : (
          <Text>No assigned lessons yet. Check back later!</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollViewContainer: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  welcomeText: {
    fontSize: 18,
    marginBottom: 30,
    color: '#555',
  },
  contentCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginVertical: 10,
    width: '95%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
    color: '#333',
  },
  contentStatus: {
    fontSize: 14,
    color: '#666',
  },
  startButton: {
    backgroundColor: '#4CAF50', // Green button
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginTop: 10,
    alignSelf: 'flex-end',
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
});


export default StudentHomeScreen;
