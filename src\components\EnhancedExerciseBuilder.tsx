import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  Alert
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/theme';
import { <PERSON>, Button } from './';

interface QuestionOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

interface DragDropItem {
  id: string;
  text: string;
  category: string;
}

interface Question {
  id: string;
  type: 'multiple_choice' | 'drag_drop' | 'fill_blank' | 'matching' | 'drawing' | 'audio';
  title: string;
  instruction: string;
  content: any; // Different content based on question type
  difficulty: 'Easy' | 'Medium' | 'Hard';
  subject: string;
  ageGroup: 'KG' | '1st' | '2nd' | '3rd' | '4th' | '5th';
}

interface EnhancedExerciseBuilderProps {
  onSaveExercise: (questions: Question[]) => void;
  existingQuestions?: Question[];
}

const EnhancedExerciseBuilder: React.FC<EnhancedExerciseBuilderProps> = ({
  onSaveExercise,
  existingQuestions = []
}) => {
  const [questions, setQuestions] = useState<Question[]>(existingQuestions);
  const [currentQuestion, setCurrentQuestion] = useState<Partial<Question>>({
    type: 'multiple_choice',
    difficulty: 'Easy',
    subject: 'Mathematics',
    ageGroup: 'KG'
  });

  const questionTypes = [
    { value: 'multiple_choice', label: 'Multiple Choice', icon: '✓', description: 'Choose the correct answer' },
    { value: 'drag_drop', label: 'Drag & Drop', icon: '⟷', description: 'Drag items to correct positions' },
    { value: 'fill_blank', label: 'Fill Blanks', icon: '___', description: 'Complete the sentence' },
    { value: 'matching', label: 'Matching', icon: '⟷', description: 'Match related items' },
    { value: 'drawing', label: 'Drawing', icon: '✏️', description: 'Draw or trace shapes' },
    { value: 'audio', label: 'Audio', icon: '🎤', description: 'Record pronunciation' }
  ];

  const difficulties = ['Easy', 'Medium', 'Hard'];
  const subjects = ['Mathematics', 'Language Arts', 'Science', 'Social Studies', 'Art'];
  const ageGroups = ['KG', '1st', '2nd', '3rd', '4th', '5th'];

  const addQuestion = () => {
    if (!currentQuestion.title || !currentQuestion.instruction) {
      Alert.alert('Error', 'Please fill in title and instruction');
      return;
    }

    const newQuestion: Question = {
      id: `q_${Date.now()}`,
      type: currentQuestion.type as Question['type'],
      title: currentQuestion.title!,
      instruction: currentQuestion.instruction!,
      content: generateDefaultContent(currentQuestion.type as Question['type']),
      difficulty: currentQuestion.difficulty as Question['difficulty'],
      subject: currentQuestion.subject!,
      ageGroup: currentQuestion.ageGroup as Question['ageGroup']
    };

    setQuestions([...questions, newQuestion]);
    setCurrentQuestion({
      type: 'multiple_choice',
      difficulty: 'Easy',
      subject: 'Mathematics',
      ageGroup: 'KG'
    });
  };

  const generateDefaultContent = (type: Question['type']) => {
    switch (type) {
      case 'multiple_choice':
        return {
          question: '',
          options: [
            { id: '1', text: '', isCorrect: true },
            { id: '2', text: '', isCorrect: false },
            { id: '3', text: '', isCorrect: false },
            { id: '4', text: '', isCorrect: false }
          ]
        };
      case 'drag_drop':
        return {
          instruction: 'Drag the items to their correct categories',
          categories: ['Category 1', 'Category 2'],
          items: [
            { id: '1', text: 'Item 1', category: 'Category 1' },
            { id: '2', text: 'Item 2', category: 'Category 2' }
          ]
        };
      case 'fill_blank':
        return {
          sentence: 'The cat is _____ on the mat.',
          blanks: [{ position: 1, correctAnswer: 'sitting', alternatives: ['running', 'jumping'] }]
        };
      case 'matching':
        return {
          leftItems: ['Apple', 'Car', 'Book'],
          rightItems: ['Fruit', 'Vehicle', 'Object'],
          correctPairs: [
            { left: 'Apple', right: 'Fruit' },
            { left: 'Car', right: 'Vehicle' },
            { left: 'Book', right: 'Object' }
          ]
        };
      case 'drawing':
        return {
          canvasSize: { width: 300, height: 200 },
          targetShape: 'circle',
          strokeColor: '#000000',
          strokeWidth: 3
        };
      case 'audio':
        return {
          targetWord: 'apple',
          phonetic: '/ˈæpəl/',
          sampleAudioUrl: '',
          maxRecordingTime: 5
        };
      default:
        return {};
    }
  };

  const renderQuestionTypeSelector = () => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Question Type</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.typeSelector}>
        {questionTypes.map((type) => (
          <TouchableOpacity
            key={type.value}
            style={[
              styles.typeCard,
              currentQuestion.type === type.value && styles.typeCardSelected
            ]}
            onPress={() => setCurrentQuestion({ ...currentQuestion, type: type.value as Question['type'] })}
          >
            <Text style={styles.typeIcon}>{type.icon}</Text>
            <Text style={styles.typeLabel}>{type.label}</Text>
            <Text style={styles.typeDescription}>{type.description}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderBasicFields = () => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Basic Information</Text>
      
      <TextInput
        style={styles.input}
        placeholder="Question Title"
        value={currentQuestion.title || ''}
        onChangeText={(text) => setCurrentQuestion({ ...currentQuestion, title: text })}
      />
      
      <TextInput
        style={[styles.input, styles.textArea]}
        placeholder="Instructions for students"
        value={currentQuestion.instruction || ''}
        onChangeText={(text) => setCurrentQuestion({ ...currentQuestion, instruction: text })}
        multiline
        numberOfLines={3}
      />
      
      <View style={styles.selectorsRow}>
        <View style={styles.selectorContainer}>
          <Text style={styles.selectorLabel}>Difficulty</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {difficulties.map((diff) => (
              <TouchableOpacity
                key={diff}
                style={[
                  styles.selectorChip,
                  currentQuestion.difficulty === diff && styles.selectorChipSelected
                ]}
                onPress={() => setCurrentQuestion({ ...currentQuestion, difficulty: diff as Question['difficulty'] })}
              >
                <Text style={[
                  styles.selectorChipText,
                  currentQuestion.difficulty === diff && styles.selectorChipTextSelected
                ]}>{diff}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        <View style={styles.selectorContainer}>
          <Text style={styles.selectorLabel}>Subject</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {subjects.map((subject) => (
              <TouchableOpacity
                key={subject}
                style={[
                  styles.selectorChip,
                  currentQuestion.subject === subject && styles.selectorChipSelected
                ]}
                onPress={() => setCurrentQuestion({ ...currentQuestion, subject })}
              >
                <Text style={[
                  styles.selectorChipText,
                  currentQuestion.subject === subject && styles.selectorChipTextSelected
                ]}>{subject}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </View>
  );

  const renderQuestionsList = () => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Created Questions ({questions.length})</Text>
      {questions.map((question, index) => (
        <Card key={question.id} style={styles.questionCard}>
          <View style={styles.questionHeader}>
            <View style={styles.questionInfo}>
              <Text style={styles.questionTitle}>{question.title}</Text>
              <Text style={styles.questionMeta}>
                {question.type.replace('_', ' ').toUpperCase()} • {question.difficulty} • {question.subject}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => setQuestions(questions.filter(q => q.id !== question.id))}
            >
              <Text style={styles.deleteButtonText}>🗑️</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.questionInstruction}>{question.instruction}</Text>
        </Card>
      ))}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Enhanced Exercise Builder</Text>
        <Text style={styles.subtitle}>Create engaging interactive exercises for students</Text>
      </View>

      {renderQuestionTypeSelector()}
      {renderBasicFields()}

      <View style={styles.actionContainer}>
        <Button
          title="Add Question"
          onPress={addQuestion}
          variant="teacher"
          size="lg"
          style={styles.addButton}
        />
      </View>

      {questions.length > 0 && renderQuestionsList()}

      {questions.length > 0 && (
        <View style={styles.actionContainer}>
          <Button
            title={`Save Exercise (${questions.length} questions)`}
            onPress={() => onSaveExercise(questions)}
            variant="primary"
            size="lg"
            fullWidth
          />
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  header: {
    padding: Spacing.lg,
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
    marginBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  sectionContainer: {
    backgroundColor: Colors.background.primary,
    margin: Spacing.md,
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    elevation: 2,
    shadowColor: Colors.neutral[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  typeSelector: {
    marginHorizontal: -Spacing.xs,
  },
  typeCard: {
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginHorizontal: Spacing.xs,
    minWidth: 120,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  typeCardSelected: {
    backgroundColor: Colors.teacher.primary + '15',
    borderColor: Colors.teacher.primary,
  },
  typeIcon: {
    fontSize: Typography.fontSize['2xl'],
    marginBottom: Spacing.xs,
  },
  typeLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs / 2,
    textAlign: 'center',
  },
  typeDescription: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.neutral[300],
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: Typography.fontSize.base,
    marginBottom: Spacing.md,
    backgroundColor: Colors.background.primary,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  selectorsRow: {
    gap: Spacing.md,
  },
  selectorContainer: {
    marginBottom: Spacing.md,
  },
  selectorLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  selectorChip: {
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    marginRight: Spacing.xs,
    borderWidth: 1,
    borderColor: Colors.neutral[300],
  },
  selectorChipSelected: {
    backgroundColor: Colors.teacher.primary,
    borderColor: Colors.teacher.primary,
  },
  selectorChipText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.primary,
  },
  selectorChipTextSelected: {
    color: Colors.text.inverse,
    fontWeight: '600' as const,
  },
  actionContainer: {
    padding: Spacing.md,
  },
  addButton: {
    marginBottom: Spacing.md,
  },
  questionCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  questionInfo: {
    flex: 1,
  },
  questionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '600' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs / 2,
  },
  questionMeta: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  questionInstruction: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    fontStyle: 'italic',
  },
  deleteButton: {
    padding: Spacing.xs,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.error[50],
  },
  deleteButtonText: {
    fontSize: Typography.fontSize.lg,
  },
});

export default EnhancedExerciseBuilder;
