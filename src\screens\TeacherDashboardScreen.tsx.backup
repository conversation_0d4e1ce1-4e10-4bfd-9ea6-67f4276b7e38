import React from 'react';
import { View, Text, StyleSheet, Button } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';

type TeacherDashboardScreenProps = NativeStackScreenProps<RootStackParamList, 'TeacherDashboard'>;

const TeacherDashboardScreen = ({ navigation }: TeacherDashboardScreenProps) => {
  const handleManageStudents = () => {
    // Navigate to a student management screen (to be created later)
    console.log('Manage Students pressed');
  };

  const handleViewReports = () => {
    // Navigate to a reports screen (to be created later)
    console.log('View Reports pressed');
  };

  const handleManageContent = () => {
    navigation.navigate('LessonList');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Teacher Dashboard</Text>
      <Text style={styles.welcomeText}>Welcome, Teacher!</Text>
      
      <View style={styles.buttonContainer}>
        <Button title="Manage Students" onPress={handleManageStudents} />
      </View>
      <View style={styles.buttonContainer}>
        <Button title="View Progress Reports" onPress={handleViewReports} />
      </View>
      <View style={styles.buttonContainer}>
        <Button title="Manage Content" onPress={handleManageContent} />
      </View>
      <View style={styles.buttonContainer}>
        <Button title="Manage Exercises" onPress={() => navigation.navigate('ExerciseList')} />
      </View>
      {/* More options will be added here */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  welcomeText: {
    fontSize: 18,
    marginBottom: 30,
  },
  buttonContainer: {
    width: '80%',
    marginVertical: 10,
  },
});

export default TeacherDashboardScreen;
