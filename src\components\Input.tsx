import React from 'react';
import {
  TextInput,
  View,
  Text,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Colors, Typography, BorderRadius, Spacing, Layout } from '../constants/theme';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  variant = 'outlined',
  size = 'md',
  containerStyle,
  labelStyle,
  errorStyle,
  style,
  ...props
}) => {
  const getInputStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      borderRadius: BorderRadius.md,
      paddingHorizontal: Spacing.md,
      fontSize: Typography.fontSize.base,
      color: Colors.text.primary,
    };

    // Size styles
    switch (size) {
      case 'sm':
        baseStyle.height = Layout.inputHeight.sm;
        baseStyle.fontSize = Typography.fontSize.sm;
        break;
      case 'lg':
        baseStyle.height = Layout.inputHeight.lg;
        baseStyle.fontSize = Typography.fontSize.lg;
        break;
      default:
        baseStyle.height = Layout.inputHeight.md;
    }

    // Variant styles
    switch (variant) {
      case 'outlined':
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = error ? Colors.error[500] : Colors.neutral[300];
        baseStyle.backgroundColor = Colors.background.primary;
        break;
      case 'filled':
        baseStyle.backgroundColor = Colors.neutral[100];
        baseStyle.borderWidth = 0;
        break;
      default:
        baseStyle.borderBottomWidth = 1;
        baseStyle.borderBottomColor = error ? Colors.error[500] : Colors.neutral[300];
        baseStyle.backgroundColor = 'transparent';
    }

    return baseStyle;
  };

  return (
    <View style={containerStyle}>
      {label && (
        <Text
          style={[
            {
              fontSize: Typography.fontSize.sm,
              fontWeight: '500' as const,
              color: Colors.text.secondary,
              marginBottom: Spacing.xs,
            },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
      <TextInput
        style={[getInputStyle(), style]}
        placeholderTextColor={Colors.text.tertiary}
        {...props}
      />
      {error && (
        <Text
          style={[
            {
              fontSize: Typography.fontSize.xs,
              color: Colors.error[500],
              marginTop: Spacing.xs,
            },
            errorStyle,
          ]}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

export default Input;
