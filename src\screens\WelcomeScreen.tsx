import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { Button, Card } from '../components';
import { Colors, Typography, Spacing, Layout } from '../constants/theme';

type WelcomeScreenProps = NativeStackScreenProps<RootStackParamList, 'Welcome'>;

const WelcomeScreen = ({ navigation }: WelcomeScreenProps) => {
  const handleTeacherAccess = () => {
    navigation.navigate('TeacherDashboard');
  };

  const handleStudentAccess = () => {
    navigation.navigate('StudentHome');
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>🎓 Teaching & Learning</Text>
          <Text style={styles.subtitle}>Choose your role to continue</Text>
        </View>

        {/* Role Selection Cards */}
        <View style={styles.roleSection}>
          <Card variant="teacher" style={styles.roleCard}>
            <View style={styles.roleContent}>
              <Text style={styles.roleIcon}>👩‍🏫</Text>
              <Text style={styles.roleTitle}>Teacher</Text>
              <Text style={styles.roleDescription}>
                Access your dashboard to manage students, create content, and track progress
              </Text>
              <Button
                title="Continue as Teacher"
                onPress={handleTeacherAccess}
                variant="teacher"
                fullWidth
                style={styles.roleButton}
              />
            </View>
          </Card>

          <Card variant="student" style={styles.roleCard}>
            <View style={styles.roleContent}>
              <Text style={styles.roleIcon}>👨‍🎓</Text>
              <Text style={styles.roleTitle}>Student</Text>
              <Text style={styles.roleDescription}>
                Start your learning journey with fun lessons and interactive exercises
              </Text>
              <Button
                title="Continue as Student"
                onPress={handleStudentAccess}
                variant="student"
                fullWidth
                style={styles.roleButton}
              />
            </View>
          </Card>
        </View>

        {/* Back to Login */}
        <View style={styles.footer}>
          <Button
            title="← Back to Login"
            onPress={handleBackToLogin}
            variant="ghost"
            style={styles.backButton}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  content: {
    flex: 1,
    padding: Layout.screenPadding,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: '700' as const,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.lg,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  roleSection: {
    gap: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  roleCard: {
    marginBottom: Spacing.sm,
  },
  roleContent: {
    alignItems: 'center',
  },
  roleIcon: {
    fontSize: 48,
    marginBottom: Spacing.md,
  },
  roleTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  roleDescription: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  roleButton: {
    minWidth: 200,
  },
  footer: {
    alignItems: 'center',
  },
  backButton: {
    paddingHorizontal: Spacing.lg,
  },
});

export default WelcomeScreen;
