import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface OfflineAction {
  id: string;
  type: string;
  payload: any;
  timestamp: number;
}

interface OfflineState {
  isOnline: boolean;
  pendingActions: OfflineAction[];
  lastSyncTime?: number;
}

const initialState: OfflineState = {
  isOnline: true,
  pendingActions: [],
};

const offlineSlice = createSlice({
  name: 'offline',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    
    addPendingAction: (state, action: PayloadAction<Omit<OfflineAction, 'id' | 'timestamp'>>) => {
      const pendingAction: OfflineAction = {
        id: `${Date.now()}_${Math.random()}`,
        timestamp: Date.now(),
        ...action.payload,
      };
      state.pendingActions.push(pendingAction);
    },
    
    removePendingAction: (state, action: PayloadAction<string>) => {
      state.pendingActions = state.pendingActions.filter(
        action => action.id !== action.payload
      );
    },
    
    clearPendingActions: (state) => {
      state.pendingActions = [];
    },
    
    setLastSyncTime: (state, action: PayloadAction<number>) => {
      state.lastSyncTime = action.payload;
    },
  },
});

export const {
  setOnlineStatus,
  addPendingAction,
  removePendingAction,
  clearPendingActions,
  setLastSyncTime,
} = offlineSlice.actions;

export default offlineSlice.reducer;
